package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.BaiHoc;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * BaiHocRepository - Repository cho entity BaiHoc
 */
@Repository
public interface BaiHocRepository extends JpaRepository<BaiHoc, Long> {

    /**
     * Tìm bài học theo mã bài học
     */
    Optional<BaiHoc> findByMaBaiHoc(String maBaiHoc);

    /**
     * Kiểm tra tồn tại theo mã bài học
     */
    boolean existsByMaBaiHoc(String maBaiHoc);

    /**
     * Kiểm tra tồn tại theo mã bài học và loại trừ ID
     */
    boolean existsByMaBaiHocAndIdBaiHocNot(String maBaiHoc, Long idBaiHoc);

    /**
     * Tìm tất cả bài học theo môn học
     */
    List<BaiHoc> findByIdMonHoc(Long idMonHoc);

    /**
     * Tìm bài học theo môn học và trạng thái
     */
    List<BaiHoc> findByIdMonHocAndTrangThaiTrue(Long idMonHoc);

    /**
     * Tìm bài học theo môn học với phân trang
     */
    @Query("SELECT bh FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc ORDER BY bh.sttBaiHoc ASC")
    Page<BaiHoc> findByIdMonHocWithPagination(@Param("idMonHoc") Long idMonHoc, Pageable pageable);

    /**
     * Tìm bài học theo môn học và sắp xếp theo số thứ tự
     */
    @Query("SELECT bh FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc ORDER BY bh.sttBaiHoc ASC")
    List<BaiHoc> findByIdMonHocOrderBySttBaiHoc(@Param("idMonHoc") Long idMonHoc);

    /**
     * Tìm bài học theo từ khóa
     */
    @Query("SELECT bh FROM BaiHoc bh WHERE " +
           "(LOWER(bh.tenBaiHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(bh.maBaiHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(bh.noiDung) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<BaiHoc> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * Tìm bài học theo môn học và từ khóa
     */
    @Query("SELECT bh FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND " +
           "(LOWER(bh.tenBaiHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(bh.maBaiHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(bh.noiDung) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<BaiHoc> findByIdMonHocAndKeyword(@Param("idMonHoc") Long idMonHoc, 
                                          @Param("keyword") String keyword, 
                                          Pageable pageable);

    /**
     * Đếm số bài học theo môn học
     */
    @Query("SELECT COUNT(bh) FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc")
    Long countByIdMonHoc(@Param("idMonHoc") Long idMonHoc);

    /**
     * Đếm số bài học theo môn học và trạng thái
     */
    @Query("SELECT COUNT(bh) FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND bh.trangThai = true")
    Long countByIdMonHocAndTrangThaiTrue(@Param("idMonHoc") Long idMonHoc);

    /**
     * Tìm số thứ tự bài học lớn nhất trong môn học
     */
    @Query("SELECT MAX(bh.sttBaiHoc) FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc")
    Integer findMaxSttBaiHocByIdMonHoc(@Param("idMonHoc") Long idMonHoc);

    /**
     * Kiểm tra trùng số thứ tự trong môn học
     */
    @Query("SELECT COUNT(bh) > 0 FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND bh.sttBaiHoc = :sttBaiHoc")
    boolean existsByIdMonHocAndSttBaiHoc(@Param("idMonHoc") Long idMonHoc, @Param("sttBaiHoc") Integer sttBaiHoc);

    /**
     * Kiểm tra trùng số thứ tự trong môn học (loại trừ ID hiện tại)
     */
    @Query("SELECT COUNT(bh) > 0 FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND bh.sttBaiHoc = :sttBaiHoc AND bh.idBaiHoc != :idBaiHoc")
    boolean existsByIdMonHocAndSttBaiHocAndIdBaiHocNot(@Param("idMonHoc") Long idMonHoc, 
                                                       @Param("sttBaiHoc") Integer sttBaiHoc, 
                                                       @Param("idBaiHoc") Long idBaiHoc);

    /**
     * Tính tổng số tiết của tất cả bài học trong môn
     */
    @Query("SELECT COALESCE(SUM(bh.soTietLt + bh.soTietTh + bh.soTietTu), 0) FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND bh.trangThai = true")
    Integer sumTotalHoursByIdMonHoc(@Param("idMonHoc") Long idMonHoc);

    /**
     * Tìm bài học theo khoảng số thứ tự
     */
    @Query("SELECT bh FROM BaiHoc bh WHERE bh.idMonHoc = :idMonHoc AND bh.sttBaiHoc BETWEEN :fromStt AND :toStt ORDER BY bh.sttBaiHoc ASC")
    List<BaiHoc> findByIdMonHocAndSttBaiHocBetween(@Param("idMonHoc") Long idMonHoc, 
                                                   @Param("fromStt") Integer fromStt, 
                                                   @Param("toStt") Integer toStt);
}
