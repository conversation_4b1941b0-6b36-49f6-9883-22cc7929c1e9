package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.MasterDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MasterDataServiceImpl - Implementation của MasterDataService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MasterDataServiceImpl implements MasterDataService {

    private final PBMMRepository pbmmRepository;
    private final MonHocRepository monHocRepository;
    private final LopHocRepository lopHocRepository;
    private final PhongHocRepository phongHocRepository;
    private final CoSoRepository coSoRepository;
    private final CanBoRepository canBoRepository;
    private final NganhHocRepository nganhHocRepository;
    private final PasswordEncoder passwordEncoder;

    // ==================== QUẢN LÝ KHOA/PHÒNG BAN ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllDepartments(Pageable pageable) {
        log.info("Getting all departments with pagination: page={}, size={}", pageable.getPageNumber(), pageable.getPageSize());

        try {
            Page<Khoa> pbmmPage = pbmmRepository.findAll(pageable);

            log.debug("Found {} departments total, {} on current page", pbmmPage.getTotalElements(), pbmmPage.getNumberOfElements());

            if (pbmmPage.isEmpty()) {
                log.warn("No departments found");
                return PageResponse.builder()
                        .content(new ArrayList<>())
                        .page(pageable.getPageNumber())
                        .size(pageable.getPageSize())
                        .totalElements(0L)
                        .totalPages(0)
                        .first(true)
                        .last(true)
                        .hasNext(false)
                        .hasPrevious(false)
                        .build();
            }

            List<Object> responses = pbmmPage.getContent().stream()
                    .map(this::mapPBMMToResponse)
                    .collect(Collectors.toList());

            log.debug("Successfully mapped {} department responses", responses.size());

            return PageResponse.builder()
                    .content(responses)
                    .page(pbmmPage.getNumber())
                    .size(pbmmPage.getSize())
                    .totalElements(pbmmPage.getTotalElements())
                    .totalPages(pbmmPage.getTotalPages())
                    .first(pbmmPage.isFirst())
                    .last(pbmmPage.isLast())
                    .hasNext(pbmmPage.hasNext())
                    .hasPrevious(pbmmPage.hasPrevious())
                    .build();

        } catch (Exception e) {
            log.error("Error getting departments: ", e);
            throw new RuntimeException("Lỗi khi lấy danh sách khoa: " + e.getMessage(), e);
        }
    }

    @Override
    public Object createDepartment(DepartmentRequest request) {
        log.info("Creating new department: {}", request.getTenKhoa());
        
        // Validate dữ liệu
        validateDepartmentRequest(request, null);
        
        // Tạo entity mới
        Khoa pbmm = new Khoa();
        mapDepartmentRequestToEntity(request, pbmm);
        
        // Lưu vào database
        pbmm = pbmmRepository.save(pbmm);
        
        log.info("Department created successfully with ID: {}", pbmm.getIdKhoa());
        return mapPBMMToResponse(pbmm);
    }

    @Override
    public Object updateDepartment(Long id, DepartmentRequest request) {
        log.info("Updating department {}: {}", id, request.getTenKhoa());
        
        // Kiểm tra tồn tại
        Khoa existingPBMM = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        // Validate dữ liệu
        validateDepartmentRequest(request, id);
        
        // Cập nhật thông tin
        mapDepartmentRequestToEntity(request, existingPBMM);
        
        // Lưu vào database
        existingPBMM = pbmmRepository.save(existingPBMM);
        
        log.info("Department updated successfully: {}", id);
        return mapPBMMToResponse(existingPBMM);
    }

    @Override
    public void deleteDepartment(Long id) {
        log.info("Deleting department with ID: {}", id);
        
        // Kiểm tra tồn tại
        Khoa pbmm = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        // Kiểm tra có thể xóa không (có cán bộ, môn học liên quan)
        if (canBoRepository.countByIdKhoa(id) > 0) {
            throw new BadRequestException("Không thể xóa khoa này vì đã có cán bộ");
        }
        
        if (monHocRepository.countByIdKhoa(id) > 0) {
            throw new BadRequestException("Không thể xóa khoa này vì đã có môn học");
        }
        
        // Xóa khoa
        pbmmRepository.delete(pbmm);
        
        log.info("Department deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getDepartmentById(Long id) {
        log.info("Getting department with ID: {}", id);
        
        Khoa pbmm = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        return mapPBMMToResponseWithDetails(pbmm);
    }

    // ==================== QUẢN LÝ MÔN HỌC ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllSubjects(Long departmentId, Pageable pageable) {
        log.info("Getting all subjects for department: {}, page: {}, size: {}", departmentId, pageable.getPageNumber(), pageable.getPageSize());

        try {
            Page<MonHoc> monHocPage;

            if (departmentId != null) {
                log.debug("Fetching subjects for department: {}", departmentId);
                monHocPage = monHocRepository.findByIdKhoaWithPagination(departmentId, pageable);
            } else {
                log.debug("Fetching all subjects");
                monHocPage = monHocRepository.findAll(pageable);
            }

            log.debug("Found {} subjects total, {} on current page", monHocPage.getTotalElements(), monHocPage.getNumberOfElements());

            if (monHocPage.isEmpty()) {
                log.warn("No subjects found for department: {}", departmentId);
                return PageResponse.builder()
                        .content(new ArrayList<>())
                        .page(pageable.getPageNumber())
                        .size(pageable.getPageSize())
                        .totalElements(0L)
                        .totalPages(0)
                        .first(true)
                        .last(true)
                        .hasNext(false)
                        .hasPrevious(false)
                        .build();
            }

            List<Object> responses = monHocPage.getContent().stream()
                    .map(this::mapMonHocToResponse)
                    .collect(Collectors.toList());

            log.debug("Successfully mapped {} subject responses", responses.size());

            return PageResponse.builder()
                    .content(responses)
                    .page(monHocPage.getNumber())
                    .size(monHocPage.getSize())
                    .totalElements(monHocPage.getTotalElements())
                    .totalPages(monHocPage.getTotalPages())
                    .first(monHocPage.isFirst())
                    .last(monHocPage.isLast())
                    .hasNext(monHocPage.hasNext())
                    .hasPrevious(monHocPage.hasPrevious())
                    .build();

        } catch (Exception e) {
            log.error("Error getting subjects for department {}: ", departmentId, e);
            throw new RuntimeException("Lỗi khi lấy danh sách môn học: " + e.getMessage(), e);
        }
    }

    @Override
    public Object createSubject(SubjectRequest request) {
        log.info("Creating new subject: {}", request.getTenMonHoc());
        
        // Validate dữ liệu
        validateSubjectRequest(request, null);
        
        // Tạo entity mới
        MonHoc monHoc = new MonHoc();
        mapSubjectRequestToEntity(request, monHoc);
        
        // Lưu vào database
        monHoc = monHocRepository.save(monHoc);
        
        log.info("Subject created successfully with ID: {}", monHoc.getIdMonHoc());
        return mapMonHocToResponse(monHoc);
    }

    @Override
    public Object updateSubject(Long id, SubjectRequest request) {
        log.info("Updating subject {}: {}", id, request.getTenMonHoc());
        
        // Kiểm tra tồn tại
        MonHoc existingMonHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        // Validate dữ liệu
        validateSubjectRequest(request, id);
        
        // Cập nhật thông tin
        mapSubjectRequestToEntity(request, existingMonHoc);
        
        // Lưu vào database
        existingMonHoc = monHocRepository.save(existingMonHoc);
        
        log.info("Subject updated successfully: {}", id);
        return mapMonHocToResponse(existingMonHoc);
    }

    @Override
    public void deleteSubject(Long id) {
        log.info("Deleting subject with ID: {}", id);
        
        // Kiểm tra tồn tại
        MonHoc monHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        // Kiểm tra có thể xóa không (có lịch giảng liên quan)
        // Long scheduleCount = lichGiangRepository.countByIdMonHoc(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa môn học này vì đã có lịch giảng");
        // }
        
        // Xóa môn học
        monHocRepository.delete(monHoc);
        
        log.info("Subject deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getSubjectById(Long id) {
        log.info("Getting subject with ID: {}", id);
        
        MonHoc monHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        return mapMonHocToResponse(monHoc);
    }

    // ==================== QUẢN LÝ LỚP HỌC ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllClasses(Long departmentId, Pageable pageable) {
        log.info("Getting all classes for department: {}", departmentId);
        
        Page<LopHoc> lopHocPage;
        
        if (departmentId != null) {
            lopHocPage = lopHocRepository.findByDepartmentWithPagination(departmentId, pageable);
        } else {
            lopHocPage = lopHocRepository.findAll(pageable);
        }
        
        List<Object> responses = lopHocPage.getContent().stream()
                .map(this::mapLopHocToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.builder()
                .content(responses)
                .page(lopHocPage.getNumber())
                .size(lopHocPage.getSize())
                .totalElements(lopHocPage.getTotalElements())
                .totalPages(lopHocPage.getTotalPages())
                .first(lopHocPage.isFirst())
                .last(lopHocPage.isLast())
                .build();
    }

    @Override
    public Object createClass(ClassRequest request) {
        log.info("Creating new class: {}", request.getTenLop());
        
        // Validate dữ liệu
        validateClassRequest(request, null);
        
        // Tạo entity mới
        LopHoc lopHoc = new LopHoc();
        mapClassRequestToEntity(request, lopHoc);
        
        // Lưu vào database
        lopHoc = lopHocRepository.save(lopHoc);
        
        log.info("Class created successfully with ID: {}", lopHoc.getIdLop());
        return mapLopHocToResponse(lopHoc);
    }

    @Override
    public Object updateClass(Long id, ClassRequest request) {
        log.info("Updating class {}: {}", id, request.getTenLop());
        
        // Kiểm tra tồn tại
        LopHoc existingLopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        // Validate dữ liệu
        validateClassRequest(request, id);
        
        // Cập nhật thông tin
        mapClassRequestToEntity(request, existingLopHoc);
        
        // Lưu vào database
        existingLopHoc = lopHocRepository.save(existingLopHoc);
        
        log.info("Class updated successfully: {}", id);
        return mapLopHocToResponse(existingLopHoc);
    }

    @Override
    public void deleteClass(Long id) {
        log.info("Deleting class with ID: {}", id);
        
        // Kiểm tra tồn tại
        LopHoc lopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        // Kiểm tra có thể xóa không
        // Long scheduleCount = lichGiangRepository.countByIdLop(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa lớp học này vì đã có lịch giảng");
        // }
        
        // Xóa lớp học
        lopHocRepository.delete(lopHoc);
        
        log.info("Class deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getClassById(Long id) {
        log.info("Getting class with ID: {}", id);
        
        LopHoc lopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        return mapLopHocToResponse(lopHoc);
    }

    // ==================== PRIVATE VALIDATION METHODS ====================
    
    private void validateDepartmentRequest(DepartmentRequest request, Long excludeId) {
        // Kiểm tra trùng mã khoa
        if (pbmmRepository.existsByMaKhoa(request.getMaKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã khoa đã tồn tại: " + request.getMaKhoa());
            }
            // Thêm logic kiểm tra cho update
        }
        
        // Kiểm tra trùng tên khoa
        if (pbmmRepository.existsByTenKhoa(request.getTenKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Tên khoa đã tồn tại: " + request.getTenKhoa());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    private void validateSubjectRequest(SubjectRequest request, Long excludeId) {
        // Kiểm tra khoa tồn tại
        pbmmRepository.findById(request.getIdKhoa())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + request.getIdKhoa()));
        
        // Kiểm tra trùng mã môn học
        if (monHocRepository.existsByMaMonHoc(request.getMaMonHoc())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã môn học đã tồn tại: " + request.getMaMonHoc());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    private void validateClassRequest(ClassRequest request, Long excludeId) {
        // Kiểm tra ngành học tồn tại
        nganhHocRepository.findById(request.getIdNganhHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy ngành học với ID: " + request.getIdNganhHoc()));
        
        // Kiểm tra trùng mã lớp
        if (lopHocRepository.existsByMaLop(request.getMaLop())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã lớp đã tồn tại: " + request.getMaLop());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    // ==================== PRIVATE MAPPING METHODS ====================
    
    private void mapDepartmentRequestToEntity(DepartmentRequest request, Khoa pbmm) {
        pbmm.setMaKhoa(request.getMaKhoa());
        pbmm.setTenKhoa(request.getTenKhoa());
        pbmm.setTrangThai(request.getTrangThai());
    }

    private void mapSubjectRequestToEntity(SubjectRequest request, MonHoc monHoc) {
        monHoc.setMaMonHoc(request.getMaMonHoc());
        monHoc.setTenMonHoc(request.getTenMonHoc());
        monHoc.setIdKhoa(request.getIdKhoa());
        monHoc.setIdLoaiMonHoc(request.getIdLoaiMonHoc());
        monHoc.setIdHeDaoTao(request.getIdHeDaoTao());
        monHoc.setIdNhomLk(request.getIdNhomLk());
        monHoc.setSoTietLt(request.getSoTietLt());
        monHoc.setSoTietTh(request.getSoTietTh());
        monHoc.setSoTietTu(request.getSoTietTu());
        monHoc.setMonDieuKien(request.getMonDieuKien());
        monHoc.setMonTn(request.getMonTn());
        monHoc.setTrangThai(true);
    }

    private void mapClassRequestToEntity(ClassRequest request, LopHoc lopHoc) {
        lopHoc.setMaLop(request.getMaLop());
        lopHoc.setTenLop(request.getTenLop());
        lopHoc.setIdNganh(request.getIdNganhHoc());
        lopHoc.setIdHeDaoTao(request.getIdHeDaoTao());
        lopHoc.setKhoaHoc(request.getKhoaHoc());
        lopHoc.setSiSo(request.getSiSo());
        lopHoc.setTrangThai(request.getTrangThai());
    }

    private Object mapPBMMToResponse(Khoa pbmm) {
        Map<String, Object> response = new HashMap<>();
        response.put("idKhoa", pbmm.getIdKhoa());
        response.put("maKhoa", pbmm.getMaKhoa());
        response.put("tenKhoa", pbmm.getTenKhoa());
        response.put("trangThai", pbmm.getTrangThai());
        response.put("ngayTao", pbmm.getNgayTao());
        response.put("ngayCapNhat", pbmm.getNgayCapNhat());
        return response;
    }

    private Object mapPBMMToResponseWithDetails(Khoa pbmm) {
        Map<String, Object> response = (Map<String, Object>) mapPBMMToResponse(pbmm);
        
        // Thêm thống kê
        Long teacherCount = canBoRepository.countByIdKhoa(pbmm.getIdKhoa());
        Long subjectCount = monHocRepository.countByIdKhoa(pbmm.getIdKhoa());
        
        response.put("soGiangVien", teacherCount);
        response.put("soMonHoc", subjectCount);
        
        return response;
    }

    private Object mapMonHocToResponse(MonHoc monHoc) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("idMonHoc", monHoc.getIdMonHoc());
            response.put("maMonHoc", monHoc.getMaMonHoc());
            response.put("tenMonHoc", monHoc.getTenMonHoc());
            response.put("idKhoa", monHoc.getIdKhoa());
            response.put("soTietLt", monHoc.getSoTietLt() != null ? monHoc.getSoTietLt() : 0);
            response.put("soTietTh", monHoc.getSoTietTh() != null ? monHoc.getSoTietTh() : 0);
            response.put("soTietTu", monHoc.getSoTietTu() != null ? monHoc.getSoTietTu() : 0);
            response.put("trangThai", monHoc.getTrangThai() != null ? monHoc.getTrangThai() : true);
            response.put("ngayTao", monHoc.getNgayTao());
            response.put("ngayCapNhat", monHoc.getNgayCapNhat());

            // Thêm thông tin khoa nếu có (tránh lazy loading exception)
            try {
                if (monHoc.getPbmm() != null) {
                    response.put("tenKhoa", monHoc.getPbmm().getTenKhoa());
                    response.put("maKhoa", monHoc.getPbmm().getMaKhoa());
                } else {
                    // Lấy thông tin khoa từ repository nếu cần
                    Khoa pbmm = pbmmRepository.findById(monHoc.getIdKhoa()).orElse(null);
                    if (pbmm != null) {
                        response.put("tenKhoa", pbmm.getTenKhoa());
                        response.put("maKhoa", pbmm.getMaKhoa());
                    }
                }
            } catch (Exception e) {
                log.warn("Could not load department info for subject {}: {}", monHoc.getIdMonHoc(), e.getMessage());
            }

            return response;
        } catch (Exception e) {
            log.error("Error mapping MonHoc to response for ID {}: {}", monHoc.getIdMonHoc(), e.getMessage());
            // Return basic info nếu có lỗi
            Map<String, Object> basicResponse = new HashMap<>();
            basicResponse.put("idMonHoc", monHoc.getIdMonHoc());
            basicResponse.put("maMonHoc", monHoc.getMaMonHoc() != null ? monHoc.getMaMonHoc() : "");
            basicResponse.put("tenMonHoc", monHoc.getTenMonHoc() != null ? monHoc.getTenMonHoc() : "");
            basicResponse.put("idKhoa", monHoc.getIdKhoa());
            basicResponse.put("soTietLt", 0);
            basicResponse.put("soTietTh", 0);
            basicResponse.put("soTietTu", 0);
            basicResponse.put("trangThai", true);
            return basicResponse;
        }
    }

    private Object mapLopHocToResponse(LopHoc lopHoc) {
        Map<String, Object> response = new HashMap<>();
        response.put("idLop", lopHoc.getIdLop());
        response.put("maLop", lopHoc.getMaLop());
        response.put("tenLop", lopHoc.getTenLop());
        response.put("idNganh", lopHoc.getIdNganh());
        response.put("idHeDaoTao", lopHoc.getIdHeDaoTao());
        response.put("khoaHoc", lopHoc.getKhoaHoc());
        response.put("siSo", lopHoc.getSiSo());
        response.put("trangThai", lopHoc.getTrangThai());
        response.put("ngayTao", lopHoc.getNgayTao());
        response.put("ngayCapNhat", lopHoc.getNgayCapNhat());
        return response;
    }

    // ==================== QUẢN LÝ PHÒNG HỌC ====================

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllRooms(Long campusId, Pageable pageable) {
        log.info("Getting all rooms for campus: {}", campusId);

        try {
            Page<PhongHoc> phongHocPage;

            if (campusId != null) {
                phongHocPage = phongHocRepository.findByIdCoSoWithPagination(campusId, pageable);
            } else {
                phongHocPage = phongHocRepository.findAll(pageable);
            }

            List<Object> responses = phongHocPage.getContent().stream()
                    .map(this::mapPhongHocToResponse)
                    .collect(Collectors.toList());

            return PageResponse.builder()
                    .content(responses)
                    .page(phongHocPage.getNumber())
                    .size(phongHocPage.getSize())
                    .totalElements(phongHocPage.getTotalElements())
                    .totalPages(phongHocPage.getTotalPages())
                    .first(phongHocPage.isFirst())
                    .last(phongHocPage.isLast())
                    .hasNext(phongHocPage.hasNext())
                    .hasPrevious(phongHocPage.hasPrevious())
                    .build();
        } catch (Exception e) {
            log.error("Error getting rooms for campus {}: ", campusId, e);
            throw new RuntimeException("Lỗi khi lấy danh sách phòng học: " + e.getMessage(), e);
        }
    }

    @Override
    public Object createRoom(RoomRequest request) {
        log.info("Creating new room: {}", request.getTenPhong());

        // Validate dữ liệu
        validateRoomRequest(request, null);

        // Tạo entity mới
        PhongHoc phongHoc = new PhongHoc();
        mapRoomRequestToEntity(request, phongHoc);

        // Lưu vào database
        phongHoc = phongHocRepository.save(phongHoc);

        log.info("Room created successfully with ID: {}", phongHoc.getIdPhong());
        return mapPhongHocToResponse(phongHoc);
    }

    @Override
    public Object updateRoom(Long id, RoomRequest request) {
        log.info("Updating room {}: {}", id, request.getTenPhong());

        // Kiểm tra tồn tại
        PhongHoc existingPhongHoc = phongHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy phòng học với ID: " + id));

        // Validate dữ liệu
        validateRoomRequest(request, id);

        // Cập nhật thông tin
        mapRoomRequestToEntity(request, existingPhongHoc);

        // Lưu vào database
        existingPhongHoc = phongHocRepository.save(existingPhongHoc);

        log.info("Room updated successfully: {}", id);
        return mapPhongHocToResponse(existingPhongHoc);
    }

    @Override
    public void deleteRoom(Long id) {
        log.info("Deleting room with ID: {}", id);

        // Kiểm tra tồn tại
        PhongHoc phongHoc = phongHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy phòng học với ID: " + id));

        // Kiểm tra có thể xóa không (có lịch giảng liên quan)
        // Long scheduleCount = lichGiangRepository.countByIdPhong(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa phòng học này vì đã có lịch giảng");
        // }

        // Xóa phòng học
        phongHocRepository.delete(phongHoc);

        log.info("Room deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getRoomById(Long id) {
        log.info("Getting room with ID: {}", id);

        PhongHoc phongHoc = phongHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy phòng học với ID: " + id));

        return mapPhongHocToResponse(phongHoc);
    }

    // ==================== QUẢN LÝ CƠ SỞ ====================

    @Override
    @Transactional(readOnly = true)
    public List<Object> getAllCampuses() {
        log.info("Getting all campuses");

        try {
            List<CoSo> coSoList = coSoRepository.findAll();

            return coSoList.stream()
                    .map(this::mapCoSoToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error getting campuses: ", e);
            throw new RuntimeException("Lỗi khi lấy danh sách cơ sở: " + e.getMessage(), e);
        }
    }

    @Override
    public Object createCampus(CampusRequest request) {
        log.info("Creating new campus: {}", request.getTenCoSo());

        // Validate dữ liệu
        validateCampusRequest(request, null);

        // Tạo entity mới
        CoSo coSo = new CoSo();
        mapCampusRequestToEntity(request, coSo);

        // Lưu vào database
        coSo = coSoRepository.save(coSo);

        log.info("Campus created successfully with ID: {}", coSo.getIdCoSo());
        return mapCoSoToResponse(coSo);
    }

    @Override
    public Object updateCampus(Long id, CampusRequest request) {
        log.info("Updating campus {}: {}", id, request.getTenCoSo());

        // Kiểm tra tồn tại
        CoSo existingCoSo = coSoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy cơ sở với ID: " + id));

        // Validate dữ liệu
        validateCampusRequest(request, id);

        // Cập nhật thông tin
        mapCampusRequestToEntity(request, existingCoSo);

        // Lưu vào database
        existingCoSo = coSoRepository.save(existingCoSo);

        log.info("Campus updated successfully: {}", id);
        return mapCoSoToResponse(existingCoSo);
    }

    @Override
    public void deleteCampus(Long id) {
        log.info("Deleting campus with ID: {}", id);

        // Kiểm tra tồn tại
        CoSo coSo = coSoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy cơ sở với ID: " + id));

        // Kiểm tra có thể xóa không (có phòng học liên quan)
        Long roomCount = phongHocRepository.countByIdCoSo(id);
        if (roomCount > 0) {
            throw new BadRequestException("Không thể xóa cơ sở này vì đã có phòng học");
        }

        // Xóa cơ sở
        coSoRepository.delete(coSo);

        log.info("Campus deleted successfully: {}", id);
    }

    // ==================== QUẢN LÝ GIẢNG VIÊN ====================

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllTeachers(Long departmentId, Pageable pageable) {
        log.info("Getting all teachers for department: {}", departmentId);

        try {
            Page<CanBo> canBoPage;

            if (departmentId != null) {
                canBoPage = canBoRepository.findByIdKhoaWithPagination(departmentId, pageable);
            } else {
                canBoPage = canBoRepository.findAll(pageable);
            }

            List<Object> responses = canBoPage.getContent().stream()
                    .map(this::mapCanBoToResponse)
                    .collect(Collectors.toList());

            return PageResponse.builder()
                    .content(responses)
                    .page(canBoPage.getNumber())
                    .size(canBoPage.getSize())
                    .totalElements(canBoPage.getTotalElements())
                    .totalPages(canBoPage.getTotalPages())
                    .first(canBoPage.isFirst())
                    .last(canBoPage.isLast())
                    .hasNext(canBoPage.hasNext())
                    .hasPrevious(canBoPage.hasPrevious())
                    .build();
        } catch (Exception e) {
            log.error("Error getting teachers for department {}: ", departmentId, e);
            throw new RuntimeException("Lỗi khi lấy danh sách giảng viên: " + e.getMessage(), e);
        }
    }

    @Override
    public Object createTeacher(TeacherRequest request) {
        log.info("Creating new teacher: {}", request.getTen());

        // Validate dữ liệu
        validateTeacherRequest(request, null);

        // Tạo entity mới
        CanBo canBo = new CanBo();
        mapTeacherRequestToEntity(request, canBo);

        // Mã hóa mật khẩu (required cho create)
        if (request.getMatKhau() != null && !request.getMatKhau().isEmpty()) {
            canBo.setMatKhau(passwordEncoder.encode(request.getMatKhau()));
        } else {
            throw new BadRequestException("Mật khẩu không được để trống khi tạo giảng viên mới");
        }

        // Lưu vào database
        canBo = canBoRepository.save(canBo);

        log.info("Teacher created successfully with ID: {}", canBo.getIdCanBo());
        return mapCanBoToResponse(canBo);
    }

    @Override
    public Object updateTeacher(Long id, TeacherRequest request) {
        log.info("Updating teacher {}: {}", id, request.getTen());

        // Kiểm tra tồn tại
        CanBo existingCanBo = canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));

        // Validate dữ liệu
        validateTeacherRequest(request, id);

        // Cập nhật thông tin
        mapTeacherRequestToEntity(request, existingCanBo);

        // Cập nhật mật khẩu nếu có
        if (request.getMatKhau() != null && !request.getMatKhau().isEmpty()) {
            existingCanBo.setMatKhau(passwordEncoder.encode(request.getMatKhau()));
        }

        // Lưu vào database
        existingCanBo = canBoRepository.save(existingCanBo);

        log.info("Teacher updated successfully: {}", id);
        return mapCanBoToResponse(existingCanBo);
    }

    @Override
    public void deleteTeacher(Long id) {
        log.info("Deleting teacher with ID: {}", id);

        // Kiểm tra tồn tại
        CanBo canBo = canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));

        // Kiểm tra có thể xóa không (có lịch giảng liên quan)
        // Long scheduleCount = lichGiangRepository.countByIdCanBo(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa giảng viên này vì đã có lịch giảng");
        // }

        // Xóa giảng viên
        canBoRepository.delete(canBo);

        log.info("Teacher deleted successfully: {}", id);
    }

    // ==================== IMPORT/EXPORT DỮ LIỆU ====================

    @Override
    public Object importData(String dataType, MultipartFile file) {
        log.info("Importing data type: {}", dataType);

        // TODO: Implement với Apache POI để đọc Excel
        // Hiện tại trả về thông báo chưa triển khai
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Chức năng import đang được phát triển");
        result.put("dataType", dataType);
        result.put("fileName", file.getOriginalFilename());
        result.put("fileSize", file.getSize());

        return result;
    }

    @Override
    public byte[] exportTemplate(String dataType) {
        log.info("Exporting template for data type: {}", dataType);

        // TODO: Implement với Apache POI để tạo Excel template
        throw new UnsupportedOperationException("Chức năng export template đang được phát triển");
    }

    @Override
    public Object syncDataFromExternalSystem(String systemType, Object config) {
        log.info("Syncing data from external system: {}", systemType);

        // TODO: Implement đồng bộ dữ liệu từ hệ thống khác
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Chức năng đồng bộ dữ liệu đang được phát triển");
        result.put("systemType", systemType);

        return result;
    }

    // ==================== VALIDATION METHODS ====================

    private void validateRoomRequest(RoomRequest request, Long excludeId) {
        // Kiểm tra cơ sở tồn tại
        coSoRepository.findById(request.getIdCoSo())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy cơ sở với ID: " + request.getIdCoSo()));

        // Kiểm tra trùng mã phòng
        if (phongHocRepository.existsByMaPhong(request.getMaPhong())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã phòng đã tồn tại: " + request.getMaPhong());
            }
            // TODO: Thêm logic kiểm tra cho update
        }
    }

    private void validateCampusRequest(CampusRequest request, Long excludeId) {
        // Kiểm tra trùng mã cơ sở
        if (coSoRepository.existsByMaCoSo(request.getMaCoSo())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã cơ sở đã tồn tại: " + request.getMaCoSo());
            }
            // TODO: Thêm logic kiểm tra cho update
        }
    }

    private void validateTeacherRequest(TeacherRequest request, Long excludeId) {
        // Kiểm tra khoa tồn tại
        pbmmRepository.findById(request.getIdKhoa())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + request.getIdKhoa()));

        // Kiểm tra trùng mã cán bộ
        if (canBoRepository.existsByMaCanBo(request.getMaCanBo())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã cán bộ đã tồn tại: " + request.getMaCanBo());
            }
            // TODO: Thêm logic kiểm tra cho update
        }

        // Kiểm tra email trùng
        if (request.getEmail() != null && canBoRepository.existsByEmail(request.getEmail())) {
            if (excludeId == null) {
                throw new BadRequestException("Email đã tồn tại: " + request.getEmail());
            }
            // TODO: Thêm logic kiểm tra cho update
        }
    }

    // ==================== MAPPING METHODS ====================

    private void mapRoomRequestToEntity(RoomRequest request, PhongHoc phongHoc) {
        phongHoc.setMaPhong(request.getMaPhong());
        phongHoc.setTenPhong(request.getTenPhong());
        phongHoc.setLoaiPhong(request.getLoaiPhong());
        phongHoc.setSucChua(request.getSucChua());
        phongHoc.setIdCoSo(request.getIdCoSo());
        phongHoc.setTrangThai(request.getTrangThai());
    }

    private void mapCampusRequestToEntity(CampusRequest request, CoSo coSo) {
        coSo.setMaCoSo(request.getMaCoSo());
        coSo.setTenCoSo(request.getTenCoSo());
        coSo.setDiaChi(request.getDiaChi());
        coSo.setTrangThai(request.getTrangThai());
    }

    private void mapTeacherRequestToEntity(TeacherRequest request, CanBo canBo) {
        canBo.setMaCanBo(request.getMaCanBo());
        canBo.setTen(request.getTen());
        canBo.setIdKhoa(request.getIdKhoa());
        canBo.setIdVaiTro(request.getIdVaiTro());
        canBo.setNgaySinh(request.getNgaySinh());
        canBo.setNu(request.getNu());
        canBo.setSdt(request.getSdt());
        canBo.setEmail(request.getEmail());
        canBo.setTrangThai(request.getTrangThai());
    }

    private Object mapPhongHocToResponse(PhongHoc phongHoc) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("idPhong", phongHoc.getIdPhong());
            response.put("maPhong", phongHoc.getMaPhong());
            response.put("tenPhong", phongHoc.getTenPhong());
            response.put("loaiPhong", phongHoc.getLoaiPhong());
            response.put("sucChua", phongHoc.getSucChua());
            response.put("idCoSo", phongHoc.getIdCoSo());
            response.put("trangThai", phongHoc.getTrangThai());
            response.put("ngayTao", phongHoc.getNgayTao());
            response.put("ngayCapNhat", phongHoc.getNgayCapNhat());

            // Thêm thông tin cơ sở
            try {
                if (phongHoc.getCoSo() != null) {
                    response.put("tenCoSo", phongHoc.getCoSo().getTenCoSo());
                    response.put("maCoSo", phongHoc.getCoSo().getMaCoSo());
                } else {
                    CoSo coSo = coSoRepository.findById(phongHoc.getIdCoSo()).orElse(null);
                    if (coSo != null) {
                        response.put("tenCoSo", coSo.getTenCoSo());
                        response.put("maCoSo", coSo.getMaCoSo());
                    }
                }
            } catch (Exception e) {
                log.warn("Could not load campus info for room {}: {}", phongHoc.getIdPhong(), e.getMessage());
            }

            return response;
        } catch (Exception e) {
            log.error("Error mapping PhongHoc to response for ID {}: {}", phongHoc.getIdPhong(), e.getMessage());
            Map<String, Object> basicResponse = new HashMap<>();
            basicResponse.put("idPhong", phongHoc.getIdPhong());
            basicResponse.put("maPhong", phongHoc.getMaPhong());
            basicResponse.put("tenPhong", phongHoc.getTenPhong());
            return basicResponse;
        }
    }

    private Object mapCoSoToResponse(CoSo coSo) {
        Map<String, Object> response = new HashMap<>();
        response.put("idCoSo", coSo.getIdCoSo());
        response.put("maCoSo", coSo.getMaCoSo());
        response.put("tenCoSo", coSo.getTenCoSo());
        response.put("diaChi", coSo.getDiaChi());
        response.put("trangThai", coSo.getTrangThai());
        response.put("ngayTao", coSo.getNgayTao());
        response.put("ngayCapNhat", coSo.getNgayCapNhat());

        // Thêm số phòng học
        Long roomCount = phongHocRepository.countByIdCoSo(coSo.getIdCoSo());
        response.put("soPhongHoc", roomCount);

        return response;
    }

    private Object mapCanBoToResponse(CanBo canBo) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("idCanBo", canBo.getIdCanBo());
            response.put("maCanBo", canBo.getMaCanBo());
            response.put("ten", canBo.getTen());
            response.put("idKhoa", canBo.getIdKhoa());
            response.put("idVaiTro", canBo.getIdVaiTro());
            response.put("ngaySinh", canBo.getNgaySinh());
            response.put("nu", canBo.getNu());
            response.put("sdt", canBo.getSdt());
            response.put("email", canBo.getEmail());
            response.put("trangThai", canBo.getTrangThai());
            response.put("ngayTao", canBo.getNgayTao());
            response.put("ngayCapNhat", canBo.getNgayCapNhat());

            // Thêm thông tin khoa và vai trò
            try {
                if (canBo.getPbmm() != null) {
                    response.put("tenKhoa", canBo.getPbmm().getTenKhoa());
                    response.put("maKhoa", canBo.getPbmm().getMaKhoa());
                }
                if (canBo.getVaiTro() != null) {
                    response.put("tenVaiTro", canBo.getVaiTro().getTenVaiTro());
                }
            } catch (Exception e) {
                log.warn("Could not load related info for teacher {}: {}", canBo.getIdCanBo(), e.getMessage());
            }

            return response;
        } catch (Exception e) {
            log.error("Error mapping CanBo to response for ID {}: {}", canBo.getIdCanBo(), e.getMessage());
            Map<String, Object> basicResponse = new HashMap<>();
            basicResponse.put("idCanBo", canBo.getIdCanBo());
            basicResponse.put("maCanBo", canBo.getMaCanBo());
            basicResponse.put("ten", canBo.getTen());
            return basicResponse;
        }
    }
}
