package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.ClassRequest;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
/**
 * ClassServiceImpl - Implementation của ClassService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ClassServiceImpl implements ClassService {

    private final LopHocRepository lopHocRepository;
    private final NganhHocRepository nganhHocRepository;
    private final HeDaoTaoRepository heDaoTaoRepository;
    private final AuthService authService;

    @Override
    public LopHoc createClass(ClassRequest request) {
        log.info("Creating class: {}", request.getMaLop());

        // Kiểm tra quyền
        if (!authService.isAdmin() && !authService.isTruongKhoa()) {
            throw new BadRequestException("Bạn không có quyền tạo lớp học");
        }

        // Kiểm tra trùng mã lớp
        if (lopHocRepository.findByMaLop(request.getMaLop()).isPresent()) {
            throw new BadRequestException("Mã lớp đã tồn tại: " + request.getMaLop());
        }

        // Validate related entities
        NganhHoc nganh = nganhHocRepository.findById(request.getIdNganhHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy ngành học với ID: " + request.getIdNganhHoc()));

        heDaoTaoRepository.findById(request.getIdHeDaoTao())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy hệ đào tạo với ID: " + request.getIdHeDaoTao()));

        // Kiểm tra quyền tạo lớp cho khoa
        if (authService.isTruongKhoa() &&
                !authService.canAccessDepartmentData(nganh.getIdKhoa())) {
            throw new BadRequestException("Bạn chỉ có thể tạo lớp học cho khoa của mình");
        }

        // Tạo entity
        LopHoc lopHoc = new LopHoc();
        mapClassRequestToEntity(request, lopHoc);

        lopHoc = lopHocRepository.save(lopHoc);
        log.info("Class created successfully: {}", lopHoc.getMaLop());

        return lopHoc;
    }

    @Override
    public LopHoc updateClass(Long id, ClassRequest request) {
        log.info("Updating class ID: {}", id);

        LopHoc existingClass = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));

        // Kiểm tra quyền
        NganhHoc nganh = existingClass.getNganhHoc();
        if (!authService.isAdmin() &&
                (!authService.isTruongKhoa() ||
                        !authService.canAccessDepartmentData(nganh.getIdKhoa()))) {
            throw new BadRequestException("Bạn không có quyền sửa lớp học này");
        }

        // Kiểm tra trùng mã lớp (nếu thay đổi)
        if (!existingClass.getMaLop().equals(request.getMaLop())) {
            if (lopHocRepository.findByMaLop(request.getMaLop()).isPresent()) {
                throw new BadRequestException("Mã lớp đã tồn tại: " + request.getMaLop());
            }
        }

        // Validate related entities
        NganhHoc newNganh = nganhHocRepository.findById(request.getIdNganhHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy ngành học với ID: " + request.getIdNganhHoc()));

        heDaoTaoRepository.findById(request.getIdHeDaoTao())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy hệ đào tạo với ID: " + request.getIdHeDaoTao()));

        // Update entity
        mapClassRequestToEntity(request, existingClass);
        existingClass = lopHocRepository.save(existingClass);

        log.info("Class updated successfully: {}", existingClass.getMaLop());
        return existingClass;
    }

    @Override
    public void deleteClass(Long id) {
        log.info("Deleting class ID: {}", id);

        LopHoc lopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));

        // Kiểm tra quyền
        NganhHoc nganh = lopHoc.getNganhHoc();
        if (!authService.isAdmin() &&
                (!authService.isTruongKhoa() ||
                        !authService.canAccessDepartmentData(nganh.getIdKhoa()))) {
            throw new BadRequestException("Bạn không có quyền xóa lớp học này");
        }

        // Kiểm tra lớp có đang được sử dụng không
        if (lopHoc.getLichGiangList() != null && !lopHoc.getLichGiangList().isEmpty()) {
            throw new BadRequestException("Không thể xóa lớp học đang có lịch giảng");
        }

        lopHocRepository.delete(lopHoc);
        log.info("Class deleted successfully: {}", lopHoc.getMaLop());
    }

    @Override
    @Transactional(readOnly = true)
    public LopHoc getClassById(Long id) {
        return lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<LopHoc> getAllClasses(Pageable pageable) {
        return lopHocRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<LopHoc> searchClasses(String keyword, Pageable pageable) {
        return lopHocRepository.findByKeyword(keyword, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LopHoc> getClassesByMajor(Long majorId) {
        return lopHocRepository.findByIdNganh(majorId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LopHoc> getClassesByEducationLevel(Long educationLevelId) {
        return lopHocRepository.findByIdHeDaoTao(educationLevelId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LopHoc> getClassesByMajorAndEducationLevel(Long majorId, Long educationLevelId) {
        return lopHocRepository.findByNganhAndHeDaoTao(majorId, educationLevelId);
    }

    private void mapClassRequestToEntity(ClassRequest request, LopHoc entity) {
        entity.setMaLop(request.getMaLop());
        entity.setTenLop(request.getTenLop());
        entity.setIdNganh(request.getIdNganhHoc());
        entity.setIdHeDaoTao(request.getIdHeDaoTao());
        entity.setSiSo(request.getSiSo());
    }
}
