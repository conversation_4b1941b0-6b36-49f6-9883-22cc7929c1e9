package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.constant.AppConstants;
import com.university.schedulemanagement.dto.response.ScheduleResponse;
import com.university.schedulemanagement.dto.response.TeachingHourResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.AuthService;
import com.university.schedulemanagement.service.ExcelExportService;
import com.university.schedulemanagement.service.ScheduleService;
import com.university.schedulemanagement.service.TeachingHourService;
import com.university.schedulemanagement.util.TeachingHourCalculation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TeachingHourServiceImpl - Implementation của TeachingHourService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TeachingHourServiceImpl implements TeachingHourService {

    private final ThongKeGioGiangRepository thongKeGioGiangRepository;
    private final LichGiangRepository lichGiangRepository;
    private final CanBoRepository canBoRepository;
    private final HocKyRepository hocKyRepository;
    private final AuthService authService;
    private final ScheduleService scheduleService;
    private final ApplicationContext applicationContext;

    @Override
    @Async("taskExecutor")
    public void calculateTeachingHours(Long teacherId, Long semesterId) {
        log.info("Calculating teaching hours for teacher: {}, semester: {}", teacherId, semesterId);

        try {
            // Kiểm tra giảng viên và học kỳ tồn tại
            CanBo teacher = canBoRepository.findById(teacherId)
                    .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + teacherId));

            HocKy semester = hocKyRepository.findById(semesterId)
                    .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + semesterId));

            // Lấy tất cả lịch giảng của giảng viên trong học kỳ
            List<LichGiang> schedules = lichGiangRepository.findByCanBoAndHocKy(teacherId, semesterId);

            // Tính toán giờ giảng
            TeachingHourCalculation calculation = calculateHours(schedules);

            // Lưu hoặc cập nhật thống kê
            ThongKeGioGiang thongKe = thongKeGioGiangRepository
                    .findByIdCanBoAndIdHocKy(teacherId, semesterId)
                    .orElse(new ThongKeGioGiang());

            thongKe.setIdCanBo(teacherId);
            thongKe.setIdHocKy(semesterId);
            thongKe.setTongGioLt(calculation.totalLtHours);
            thongKe.setTongGioTh(calculation.totalThHours);
            thongKe.setTongGioQuyDoi(calculation.totalWeightedHours);
            thongKe.setNgayTinh(LocalDateTime.now());

            thongKeGioGiangRepository.save(thongKe);

            log.info("Teaching hours calculated successfully for teacher: {}, LT: {}, TH: {}, Total: {}",
                    teacherId, calculation.totalLtHours, calculation.totalThHours, calculation.totalWeightedHours);

        } catch (Exception e) {
            log.error("Failed to calculate teaching hours for teacher: {}, semester: {}", teacherId, semesterId, e);
            throw new RuntimeException("Lỗi khi tính toán giờ giảng: " + e.getMessage());
        }
    }

    @Override
    @Async("taskExecutor")
    public void calculateAllTeachingHours(Long semesterId) {
        log.info("Calculating teaching hours for all teachers in semester: {}", semesterId);

        // Kiểm tra quyền
        if (!authService.isAdmin() && !authService.isTruongKhoa()) {
            throw new BadRequestException("Bạn không có quyền thực hiện chức năng này");
        }

        try {
            // Lấy tất cả lịch giảng trong học kỳ
            List<LichGiang> allSchedules = lichGiangRepository.findByIdHocKyAndTrangThaiTrue(semesterId);

            // Nhóm theo giảng viên
            Map<Long, List<LichGiang>> schedulesByTeacher = allSchedules.stream()
                    .collect(Collectors.groupingBy(LichGiang::getIdCanBo));

            int totalTeachers = schedulesByTeacher.size();
            int processedTeachers = 0;

            // Tính toán cho từng giảng viên
            for (Map.Entry<Long, List<LichGiang>> entry : schedulesByTeacher.entrySet()) {
                Long teacherId = entry.getKey();
                List<LichGiang> teacherSchedules = entry.getValue();

                try {
                    // Tính toán giờ giảng
                    TeachingHourCalculation calculation = calculateHours(teacherSchedules);

                    // Lưu thống kê
                    ThongKeGioGiang thongKe = thongKeGioGiangRepository
                            .findByIdCanBoAndIdHocKy(teacherId, semesterId)
                            .orElse(new ThongKeGioGiang());

                    thongKe.setIdCanBo(teacherId);
                    thongKe.setIdHocKy(semesterId);
                    thongKe.setTongGioLt(calculation.totalLtHours);
                    thongKe.setTongGioTh(calculation.totalThHours);
                    thongKe.setTongGioQuyDoi(calculation.totalWeightedHours);
                    thongKe.setNgayTinh(LocalDateTime.now());

                    thongKeGioGiangRepository.save(thongKe);
                    processedTeachers++;

                } catch (Exception e) {
                    log.error("Failed to calculate hours for teacher: {}", teacherId, e);
                }
            }

            log.info("Teaching hours calculation completed. Processed {}/{} teachers in semester: {}",
                    processedTeachers, totalTeachers, semesterId);

        } catch (Exception e) {
            log.error("Failed to calculate all teaching hours for semester: {}", semesterId, e);
            throw new RuntimeException("Lỗi khi tính toán giờ giảng toàn học kỳ: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TeachingHourResponse getTeachingHoursByTeacher(Long teacherId, Long semesterId) {
        log.info("Getting teaching hours for teacher: {}, semester: {}", teacherId, semesterId);

        // Kiểm tra quyền truy cập
        if (!authService.canAccessTeacherData(teacherId)) {
            throw new BadRequestException("Bạn không có quyền xem giờ giảng của giảng viên này");
        }

        // Lấy thông tin giảng viên
        CanBo teacher = canBoRepository.findById(teacherId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + teacherId));

        // Lấy thông tin học kỳ
        HocKy semester = hocKyRepository.findById(semesterId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + semesterId));

        // Lấy thống kê giờ giảng
        ThongKeGioGiang thongKe = thongKeGioGiangRepository
                .findByIdCanBoAndIdHocKy(teacherId, semesterId)
                .orElse(null);

        // Nếu chưa có thống kê, tính toán ngay
        if (thongKe == null) {
            calculateTeachingHours(teacherId, semesterId);
            thongKe = thongKeGioGiangRepository
                    .findByIdCanBoAndIdHocKy(teacherId, semesterId)
                    .orElse(new ThongKeGioGiang());
        }

        // Lấy danh sách lịch giảng chi tiết
        List<ScheduleResponse> schedules = scheduleService.getSchedulesByTeacher(teacherId, semesterId);

        // Tạo response
        TeachingHourResponse response = new TeachingHourResponse();
        response.setTeacherId(teacherId);
        response.setMaCanBo(teacher.getMaCanBo());
        response.setTenCanBo(teacher.getTen());
        response.setTenKhoa(teacher.getKhoa() != null ? teacher.getKhoa().getTenKhoa() : null);
        response.setSemesterId(semesterId);
        response.setTenHocKy(semester.getTenHocKy());
        response.setTongGioLt(thongKe.getTongGioLt() != null ? thongKe.getTongGioLt() : BigDecimal.ZERO);
        response.setTongGioTh(thongKe.getTongGioTh() != null ? thongKe.getTongGioTh() : BigDecimal.ZERO);
        response.setTongGioTong(thongKe.getTongGioTong());
        response.setTongGioQuyDoi(thongKe.getTongGioQuyDoi() != null ? thongKe.getTongGioQuyDoi() : BigDecimal.ZERO);
        response.setTyLePhanBo(thongKe.getTyLePhanBo());
        response.setNgayTinh(thongKe.getNgayTinh());
        response.setLichGiangList(schedules);

        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public List<TeachingHourResponse> getTeachingHoursByDepartment(Long departmentId, Long semesterId) {
        log.info("Getting teaching hours for department: {}, semester: {}", departmentId, semesterId);

        // Kiểm tra quyền truy cập
        if (!authService.canAccessDepartmentData(departmentId)) {
            throw new BadRequestException("Bạn không có quyền xem giờ giảng của khoa này");
        }

        // Lấy danh sách thống kê theo khoa
        List<ThongKeGioGiang> thongKeList = thongKeGioGiangRepository.findByKhoaAndHocKy(departmentId, semesterId);

        return thongKeList.stream()
                .map(this::mapToTeachingHourResponse)
                .sorted((r1, r2) -> r2.getTongGioTong().compareTo(r1.getTongGioTong())) // Sắp xếp giảm dần theo tổng giờ
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public TeachingHourResponse getTeachingHoursByPeriod(Long teacherId, LocalDate fromDate, LocalDate toDate) {
        log.info("Getting teaching hours for teacher: {} from {} to {}", teacherId, fromDate, toDate);

        // Kiểm tra quyền truy cập
        if (!authService.canAccessTeacherData(teacherId)) {
            throw new BadRequestException("Bạn không có quyền xem giờ giảng của giảng viên này");
        }

        // Lấy thông tin giảng viên
        CanBo teacher = canBoRepository.findById(teacherId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + teacherId));

        // Lấy lịch giảng trong khoảng thời gian
        List<LichGiang> schedules = lichGiangRepository.findTeachingHoursByPeriod(teacherId, fromDate, toDate);

        // Tính toán tổng giờ
        TeachingHourCalculation calculation = calculateHours(schedules);

        // Lấy thống kê trong khoảng thời gian
        List<ThongKeGioGiang> periodStats = thongKeGioGiangRepository.findByCanBoAndPeriod(teacherId, fromDate, toDate);

        // Tạo response
        TeachingHourResponse response = new TeachingHourResponse();
        response.setTeacherId(teacherId);
        response.setMaCanBo(teacher.getMaCanBo());
        response.setTenCanBo(teacher.getTen());
        response.setTenKhoa(teacher.getKhoa() != null ? teacher.getKhoa().getTenKhoa() : null);
        response.setTongGioLt(calculation.totalLtHours);
        response.setTongGioTh(calculation.totalThHours);
        response.setTongGioTong(calculation.totalLtHours.add(calculation.totalThHours));
        response.setTongGioQuyDoi(calculation.totalWeightedHours);
        response.setNgayTinh(LocalDateTime.now());

        // Tính tỷ lệ phân bố
        if (calculation.totalLtHours.add(calculation.totalThHours).compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal total = calculation.totalLtHours.add(calculation.totalThHours);
            double ltPercent = calculation.totalLtHours.divide(total, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).doubleValue();
            double thPercent = calculation.totalThHours.divide(total, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).doubleValue();
            response.setTyLePhanBo(String.format("%.1f%% LT - %.1f%% TH", ltPercent, thPercent));
        } else {
            response.setTyLePhanBo("0% LT - 0% TH");
        }

        return response;
    }

    @Override
    public byte[] exportTeachingHoursReport(Long departmentId, Long semesterId) {
        log.info("Exporting teaching hours report for department: {}, semester: {}", departmentId, semesterId);
        ExcelExportService excelExportService = applicationContext.getBean(ExcelExportService.class);
        return excelExportService.exportTeachingHoursReport(departmentId, semesterId);
    }

    @Override
    @Transactional(readOnly = true)
    public TeachingHourResponse getPersonalTeachingHours(Long semesterId) {
        CanBo currentUser = authService.getCurrentUser();
        return getTeachingHoursByTeacher(currentUser.getIdCanBo(), semesterId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TeachingHourResponse> getTopTeachersByHours(Long semesterId, int limit) {
        log.info("Getting top teachers by hours for semester: {}", semesterId);

        // Lấy danh sách thống kê theo học kỳ
        List<ThongKeGioGiang> thongKeList = thongKeGioGiangRepository.findByIdHocKy(semesterId);

        return thongKeList.stream()
                .map(this::mapToTeachingHourResponse)
                .sorted((r1, r2) -> r2.getTongGioTong().compareTo(r1.getTongGioTong())) // Sắp xếp giảm dần theo tổng giờ
                .limit(limit)
                .collect(Collectors.toList());
    }

    private TeachingHourResponse mapToTeachingHourResponse(ThongKeGioGiang thongKe) {
        CanBo teacher = canBoRepository.findById(thongKe.getIdCanBo())
                .orElse(null);
        HocKy semester = hocKyRepository.findById(thongKe.getIdHocKy())
                .orElse(null);

        TeachingHourResponse response = new TeachingHourResponse();
        response.setTeacherId(thongKe.getIdCanBo());
        response.setMaCanBo(teacher != null ? teacher.getMaCanBo() : null);
        response.setTenCanBo(teacher != null ? teacher.getTen() : null);
        response.setTenKhoa(teacher != null && teacher.getKhoa() != null ? teacher.getKhoa().getTenKhoa() : null);
        response.setSemesterId(thongKe.getIdHocKy());
        response.setTenHocKy(semester != null ? semester.getTenHocKy() : null);
        response.setTongGioLt(thongKe.getTongGioLt() != null ? thongKe.getTongGioLt() : BigDecimal.ZERO);
        response.setTongGioTh(thongKe.getTongGioTh() != null ? thongKe.getTongGioTh() : BigDecimal.ZERO);
        response.setTongGioTong(thongKe.getTongGioTong());
        response.setTongGioQuyDoi(thongKe.getTongGioQuyDoi() != null ? thongKe.getTongGioQuyDoi() : BigDecimal.ZERO);
        response.setTyLePhanBo(thongKe.getTyLePhanBo());
        response.setNgayTinh(thongKe.getNgayTinh());

        return response;
    }

    /**
     * Tính toán giờ giảng từ danh sách lịch giảng
     */
    private TeachingHourCalculation calculateHours(List<LichGiang> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return new TeachingHourCalculation();
        }

        BigDecimal totalLtHours = BigDecimal.ZERO;
        BigDecimal totalThHours = BigDecimal.ZERO;
        BigDecimal totalWeightedHours = BigDecimal.ZERO;

        for (LichGiang schedule : schedules) {
            if (schedule.getSoTiet() != null && schedule.getHeSo() != null) {
                BigDecimal hours = BigDecimal.valueOf(schedule.getSoTiet())
                        .multiply(schedule.getHeSo());

                // Phân loại theo loại tiết (lý thuyết/thực hành)
                if (schedule.getNhomTh() == null || schedule.getNhomTh().trim().isEmpty()) {
                    // Lý thuyết
                    totalLtHours = totalLtHours.add(hours);
                } else {
                    // Thực hành
                    totalThHours = totalThHours.add(hours);
                }

                totalWeightedHours = totalWeightedHours.add(hours);
            }
        }

        return new TeachingHourCalculation(
                totalLtHours.setScale(2, RoundingMode.HALF_UP),
                totalThHours.setScale(2, RoundingMode.HALF_UP),
                totalWeightedHours.setScale(2, RoundingMode.HALF_UP)
        );
    }

    /**
     * So sánh giờ giảng giữa các khoa
     */
    public List<Object[]> compareTeachingHoursInDepartment(Long semesterId, Long departmentId) {
        log.info("Comparing teaching hours in department: {} for semester: {}", departmentId, semesterId);

        // Trả về danh sách so sánh giờ giảng
        // Tạm thời trả về empty list, có thể implement chi tiết sau
        return Collections.emptyList();
    }
}
