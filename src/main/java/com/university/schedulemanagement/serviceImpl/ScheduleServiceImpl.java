package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.constant.AppConstants;
import com.university.schedulemanagement.constant.MessageConstants;
import com.university.schedulemanagement.dto.request.ScheduleRequest;
import com.university.schedulemanagement.dto.response.ScheduleResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ConflictException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.AuthService;
import com.university.schedulemanagement.service.ExcelExportService;
import com.university.schedulemanagement.service.ScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ScheduleServiceImpl - Implementation của ScheduleService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ScheduleServiceImpl implements ScheduleService {

    private final LichGiangRepository lichGiangRepository;
    private final CanBoRepository canBoRepository;
    private final MonHocRepository monHocRepository;
    private final LopHocRepository lopHocRepository;
    private final PhongHocRepository phongHocRepository;
    private final HocKyRepository hocKyRepository;
    private final BuoiHocRepository buoiHocRepository;
    private final HinhThucHocRepository hinhThucHocRepository;
    private final AuthService authService;
    private final ApplicationContext applicationContext;
    private final ModelMapper modelMapper;

    @Override
    public ScheduleResponse createSchedule(ScheduleRequest request) {
        log.info("Creating schedule for teacher: {}, subject: {}, class: {}",
                request.getIdCanBo(), request.getIdMonHoc(), request.getIdLop());

        // Validate business rules
        validateScheduleRequest(request);

        // Kiểm tra xung đột
        if (checkScheduleConflict(request)) {
            throw new ConflictException(MessageConstants.ERROR_SCHEDULE_CONFLICT);
        }

        // Tạo entity mới
        LichGiang lichGiang = new LichGiang();
        mapRequestToEntity(request, lichGiang);

        // Lưu vào database
        lichGiang = lichGiangRepository.save(lichGiang);

        log.info("Schedule created successfully with ID: {}", lichGiang.getIdLichGiang());

        // Tự động cập nhật giờ giảng
        updateTeachingHours(lichGiang.getIdCanBo(), lichGiang.getIdHocKy());

        return mapEntityToResponse(lichGiang);
    }

    @Override
    public ScheduleResponse updateSchedule(Long id, ScheduleRequest request) {
        log.info("Updating schedule ID: {}", id);

        LichGiang existingSchedule = lichGiangRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lịch giảng với ID: " + id));

        // Kiểm tra quyền sửa
        if (!canModifySchedule(existingSchedule)) {
            throw new BadRequestException("Bạn không có quyền sửa lịch giảng này");
        }

        // Validate business rules
        validateScheduleRequest(request);

        // Kiểm tra xung đột (trừ chính nó)
        if (checkScheduleConflictExcluding(request, id)) {
            throw new ConflictException(MessageConstants.ERROR_SCHEDULE_CONFLICT);
        }

        // Cập nhật thông tin
        Long oldTeacherId = existingSchedule.getIdCanBo();
        Long oldSemesterId = existingSchedule.getIdHocKy();

        mapRequestToEntity(request, existingSchedule);
        existingSchedule = lichGiangRepository.save(existingSchedule);

        log.info("Schedule updated successfully: {}", id);

        // Cập nhật giờ giảng cho cả giảng viên cũ và mới (nếu khác nhau)
        updateTeachingHours(oldTeacherId, oldSemesterId);
        if (!oldTeacherId.equals(request.getIdCanBo()) || !oldSemesterId.equals(request.getIdHocKy())) {
            updateTeachingHours(request.getIdCanBo(), request.getIdHocKy());
        }

        return mapEntityToResponse(existingSchedule);
    }

    @Override
    public void deleteSchedule(Long id) {
        log.info("Deleting schedule ID: {}", id);

        LichGiang schedule = lichGiangRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lịch giảng với ID: " + id));

        // Kiểm tra quyền xóa
        if (!canModifySchedule(schedule)) {
            throw new BadRequestException("Bạn không có quyền xóa lịch giảng này");
        }

        Long teacherId = schedule.getIdCanBo();
        Long semesterId = schedule.getIdHocKy();

        // Soft delete
        schedule.setTrangThai(false);
        lichGiangRepository.save(schedule);

        log.info("Schedule deleted successfully: {}", id);

        // Cập nhật lại giờ giảng
        updateTeachingHours(teacherId, semesterId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ScheduleResponse> getSchedulesByTeacher(Long teacherId, Long semesterId) {
        log.info("Getting schedules for teacher: {}, semester: {}", teacherId, semesterId);

        // Kiểm tra quyền truy cập
        if (!authService.canAccessTeacherData(teacherId)) {
            throw new BadRequestException("Bạn không có quyền xem lịch giảng của giảng viên này");
        }

        List<LichGiang> schedules;
        if (semesterId != null) {
            schedules = lichGiangRepository.findByCanBoAndHocKy(teacherId, semesterId);
        } else {
            schedules = lichGiangRepository.findByIdCanBoAndTrangThaiTrue(teacherId);
        }

        return schedules.stream()
                .map(this::mapEntityToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ScheduleResponse> getSchedulesByClass(Long classId, Long semesterId) {
        log.info("Getting schedules for class: {}, semester: {}", classId, semesterId);

        // Kiểm tra lớp tồn tại
        LopHoc lopHoc = lopHocRepository.findById(classId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + classId));

        // Kiểm tra quyền truy cập theo khoa
        if (!authService.canAccessDepartmentData(lopHoc.getNganhHoc().getIdKhoa())) {
            throw new BadRequestException("Bạn không có quyền xem lịch giảng của lớp này");
        }

        List<LichGiang> schedules;
        if (semesterId != null) {
            schedules = lichGiangRepository.findByIdLopAndTrangThaiTrue(classId)
                    .stream()
                    .filter(s -> s.getIdHocKy().equals(semesterId))
                    .collect(Collectors.toList());
        } else {
            schedules = lichGiangRepository.findByIdLopAndTrangThaiTrue(classId);
        }

        return schedules.stream()
                .map(this::mapEntityToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ScheduleResponse> getSchedulesBySemester(Long semesterId, Pageable pageable) {
        log.info("Getting schedules for semester: {}", semesterId);

        // Kiểm tra học kỳ tồn tại
        hocKyRepository.findById(semesterId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + semesterId));

        List<LichGiang> allSchedules = lichGiangRepository.findByIdHocKyAndTrangThaiTrue(semesterId);

        // Lọc theo quyền truy cập
        List<LichGiang> accessibleSchedules = allSchedules.stream()
                .filter(this::canViewSchedule)
                .collect(Collectors.toList());

        // Phân trang thủ công
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), accessibleSchedules.size());

        List<ScheduleResponse> scheduleResponses = accessibleSchedules.subList(start, end)
                .stream()
                .map(this::mapEntityToResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(scheduleResponses, pageable, accessibleSchedules.size());
    }

    @Override
    public boolean checkScheduleConflict(ScheduleRequest request) {
        return checkScheduleConflictExcluding(request, null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PhongHoc> getAvailableRooms(Long coSoId, String loaiPhong, Integer thuHoc, Long buoiId, Long hocKyId) {
        log.info("Getting available rooms for coSo: {}, loaiPhong: {}, thu: {}, buoi: {}, hocKy: {}",
                coSoId, loaiPhong, thuHoc, buoiId, hocKyId);

        return phongHocRepository.findAvailableRooms(coSoId, loaiPhong, thuHoc, buoiId, hocKyId);
    }

    @Override
    public byte[] exportScheduleToExcel(Long teacherId, Long semesterId) {
        log.info("Exporting schedule to Excel for teacher: {}, semester: {}", teacherId, semesterId);

        if (teacherId == null) {
            // Export lịch cá nhân
            CanBo currentUser = authService.getCurrentUser();
            teacherId = currentUser.getIdCanBo();
        }

        ExcelExportService excelExportService = applicationContext.getBean(ExcelExportService.class);
        return excelExportService.exportPersonalSchedule(teacherId, semesterId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ScheduleResponse> getPersonalSchedule(Long semesterId) {
        CanBo currentUser = authService.getCurrentUser();
        return getSchedulesByTeacher(currentUser.getIdCanBo(), semesterId);
    }

    // ==================== PRIVATE HELPER METHODS ====================

    private void validateScheduleRequest(ScheduleRequest request) {
        // Validate thứ học
        if (request.getThuHoc() < AppConstants.MIN_DAY_OF_WEEK ||
                request.getThuHoc() > AppConstants.MAX_DAY_OF_WEEK) {
            throw new BadRequestException("Thứ học phải từ 2 đến 8");
        }

        // Validate hệ số
        if (request.getHeSo().doubleValue() < AppConstants.MIN_COEFFICIENT ||
                request.getHeSo().doubleValue() > AppConstants.MAX_COEFFICIENT) {
            throw new BadRequestException("Hệ số phải từ " + AppConstants.MIN_COEFFICIENT +
                    " đến " + AppConstants.MAX_COEFFICIENT);
        }

        // Validate các entity tồn tại
        validateEntitiesExist(request);

        // Validate business rules
        validateBusinessRules(request);
    }

    private void validateEntitiesExist(ScheduleRequest request) {
        // Kiểm tra giảng viên
        canBoRepository.findById(request.getIdCanBo())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên"));

        // Kiểm tra môn học
        monHocRepository.findById(request.getIdMonHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học"));

        // Kiểm tra lớp học
        lopHocRepository.findById(request.getIdLop())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học"));

        // Kiểm tra phòng học
        phongHocRepository.findById(request.getIdPhong())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy phòng học"));

        // Kiểm tra học kỳ
        hocKyRepository.findById(request.getIdHocKy())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ"));

        // Kiểm tra buổi học
        buoiHocRepository.findById(request.getIdBuoi())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy buổi học"));

        // Kiểm tra hình thức học
        hinhThucHocRepository.findById(request.getIdHinhThuc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy hình thức học"));
    }

    private void validateBusinessRules(ScheduleRequest request) {
        // Lấy thông tin môn học và phòng học
        MonHoc monHoc = monHocRepository.findById(request.getIdMonHoc()).get();
        PhongHoc phongHoc = phongHocRepository.findById(request.getIdPhong()).get();
        HinhThucHoc hinhThuc = hinhThucHocRepository.findById(request.getIdHinhThuc()).get();

        // Kiểm tra phòng học phù hợp với hình thức
        if (!phongHoc.getLoaiPhong().equals(hinhThuc.getTenHinhThuc())) {
            throw new BadRequestException("Phòng học không phù hợp với hình thức học");
        }

        // Kiểm tra môn học có thực hành nhưng chọn lý thuyết
        if ("TH".equals(hinhThuc.getTenHinhThuc()) && !monHoc.hasThucHanh()) {
            throw new BadRequestException("Môn học này không có thực hành");
        }

        // Kiểm tra môn học có lý thuyết nhưng chọn thực hành
        if ("LT".equals(hinhThuc.getTenHinhThuc()) && !monHoc.hasLyThuyet()) {
            throw new BadRequestException("Môn học này không có lý thuyết");
        }

        // Kiểm tra số tiết hợp lý
        int maxTiet = "LT".equals(hinhThuc.getTenHinhThuc()) ? monHoc.getSoTietLt() : monHoc.getSoTietTh();
        if (request.getSoTiet() > maxTiet) {
            throw new BadRequestException("Số tiết vượt quá số tiết cho phép của môn học");
        }

        // Kiểm tra nhóm thực hành
        if ("TH".equals(hinhThuc.getTenHinhThuc()) &&
                (request.getNhomTh() == null || request.getNhomTh().trim().isEmpty())) {
            throw new BadRequestException("Phải nhập nhóm thực hành");
        }
    }

    private boolean checkScheduleConflictExcluding(ScheduleRequest request, Long excludeId) {
        // Kiểm tra xung đột phòng học
        List<LichGiang> roomConflicts = lichGiangRepository.findConflictSchedule(
                request.getIdPhong(), request.getThuHoc(), request.getIdBuoi(), request.getIdHocKy());

        if (excludeId != null) {
            roomConflicts = roomConflicts.stream()
                    .filter(s -> !s.getIdLichGiang().equals(excludeId))
                    .collect(Collectors.toList());
        }

        if (!roomConflicts.isEmpty()) {
            log.warn("Room conflict detected for room: {}, day: {}, session: {}",
                    request.getIdPhong(), request.getThuHoc(), request.getIdBuoi());
            return true;
        }

        // Kiểm tra xung đột giảng viên
        List<LichGiang> teacherConflicts = lichGiangRepository.findTeacherConflictSchedule(
                request.getIdCanBo(), request.getThuHoc(), request.getIdBuoi(), request.getIdHocKy());

        if (excludeId != null) {
            teacherConflicts = teacherConflicts.stream()
                    .filter(s -> !s.getIdLichGiang().equals(excludeId))
                    .collect(Collectors.toList());
        }

        if (!teacherConflicts.isEmpty()) {
            log.warn("Teacher conflict detected for teacher: {}, day: {}, session: {}",
                    request.getIdCanBo(), request.getThuHoc(), request.getIdBuoi());
            return true;
        }

        return false;
    }

    private void mapRequestToEntity(ScheduleRequest request, LichGiang entity) {
        entity.setIdHocKy(request.getIdHocKy());
        entity.setIdLop(request.getIdLop());
        entity.setIdMonHoc(request.getIdMonHoc());
        entity.setIdCanBo(request.getIdCanBo());
        entity.setIdHinhThuc(request.getIdHinhThuc());
        entity.setIdPhong(request.getIdPhong());
        entity.setIdBuoi(request.getIdBuoi());
        entity.setThuHoc(request.getThuHoc());
        entity.setSoTiet(request.getSoTiet());
        entity.setHeSo(request.getHeSo());
        entity.setNhomTh(request.getNhomTh());
        entity.setTuanHoc(request.getTuanHoc());
        entity.setGhiChu(request.getGhiChu());
        entity.setTrangThai(true);
    }

    private ScheduleResponse mapEntityToResponse(LichGiang entity) {
        ScheduleResponse response = new ScheduleResponse();
        response.setId(entity.getIdLichGiang());
        response.setSoTiet(entity.getSoTiet());
        response.setHeSo(entity.getHeSo());
        response.setSoGioQuyDoi(entity.getSoGioQuyDoi());
        response.setThuHoc(entity.getThuHoc());
        response.setThuHocText(entity.getThuHocText());
        response.setNhomTh(entity.getNhomTh());
        response.setTuanHoc(entity.getTuanHoc());
        response.setGhiChu(entity.getGhiChu());
        response.setTrangThai(entity.getTrangThai());
        response.setCreatedAt(entity.getNgayTao());
        response.setUpdatedAt(entity.getNgayCapNhat());

        // Map related entities
        if (entity.getMonHoc() != null) {
            response.setMaMonHoc(entity.getMonHoc().getMaMonHoc());
            response.setTenMonHoc(entity.getMonHoc().getTenMonHoc());
        }

        if (entity.getCanBo() != null) {
            response.setTenGiangVien(entity.getCanBo().getTen());
        }

        if (entity.getLopHoc() != null) {
            response.setMaLop(entity.getLopHoc().getMaLop());
            response.setTenLop(entity.getLopHoc().getTenLop());
        }

        if (entity.getHinhThucHoc() != null) {
            response.setHinhThucHoc(entity.getHinhThucHoc().getTenHinhThuc());
        }

        if (entity.getBuoiHoc() != null) {
            response.setTenBuoi(entity.getBuoiHoc().getTenBuoi());
            response.setGioBatDau(entity.getBuoiHoc().getGioBatDau());
            response.setGioKetThuc(entity.getBuoiHoc().getGioKetThuc());
        }

        if (entity.getPhongHoc() != null) {
            response.setTenPhong(entity.getPhongHoc().getTenPhong());
            if (entity.getPhongHoc().getCoSo() != null) {
                response.setTenCoSo(entity.getPhongHoc().getCoSo().getTenCoSo());
            }
        }

        return response;
    }

    private boolean canModifySchedule(LichGiang schedule) {
        // Admin có thể sửa tất cả
        if (authService.isAdmin()) {
            return true;
        }

        // Trưởng khoa có thể sửa lịch trong khoa
        if (authService.isTruongKhoa()) {
            CanBo currentUser = authService.getCurrentUser();
            CanBo teacher = canBoRepository.findById(schedule.getIdCanBo()).orElse(null);
            return teacher != null && currentUser.getIdKhoa().equals(teacher.getIdKhoa());
        }

        return false;
    }

    private boolean canViewSchedule(LichGiang schedule) {
        // Admin có thể xem tất cả
        if (authService.isAdmin()) {
            return true;
        }

        // Trưởng khoa có thể xem lịch trong khoa
        if (authService.isTruongKhoa()) {
            CanBo currentUser = authService.getCurrentUser();
            CanBo teacher = canBoRepository.findById(schedule.getIdCanBo()).orElse(null);
            return teacher != null && currentUser.getIdKhoa().equals(teacher.getIdKhoa());
        }

        // Giảng viên chỉ xem được lịch của mình
        if (authService.isGiangVien()) {
            CanBo currentUser = authService.getCurrentUser();
            return currentUser.getIdCanBo().equals(schedule.getIdCanBo());
        }

        return false;
    }

    private void updateTeachingHours(Long teacherId, Long semesterId) {
        try {
            // Gọi service tính toán giờ giảng (sẽ implement sau)
            // teachingHourService.calculateTeachingHours(teacherId, semesterId);
            log.debug("Teaching hours updated for teacher: {}, semester: {}", teacherId, semesterId);
        } catch (Exception e) {
            log.error("Failed to update teaching hours for teacher: {}, semester: {}", teacherId, semesterId, e);
        }
    }

    /**
     * Lấy lịch giảng theo tuần
     */
    @Transactional(readOnly = true)
    public List<ScheduleResponse> getWeeklySchedule(Long teacherId, Long semesterId, int weekNumber) {
        log.info("Getting weekly schedule for teacher: {}, semester: {}, week: {}", teacherId, semesterId, weekNumber);

        if (!authService.canAccessTeacherData(teacherId)) {
            throw new BadRequestException("Bạn không có quyền xem lịch giảng của giảng viên này");
        }

        List<LichGiang> schedules = lichGiangRepository.findByCanBoAndHocKy(teacherId, semesterId)
                .stream()
                .filter(s -> isScheduleInWeek(s, weekNumber))
                .collect(Collectors.toList());

        return schedules.stream()
                .map(this::mapEntityToResponse)
                .sorted((s1, s2) -> {
                    // Sắp xếp theo thứ, rồi theo buổi
                    int dayCompare = s1.getThuHoc().compareTo(s2.getThuHoc());
                    if (dayCompare != 0) return dayCompare;
                    return s1.getGioBatDau().compareTo(s2.getGioBatDau());
                })
                .collect(Collectors.toList());
    }

    private boolean isScheduleInWeek(LichGiang schedule, int weekNumber) {
        String tuanHoc = schedule.getTuanHoc();
        if (tuanHoc == null || tuanHoc.trim().isEmpty()) {
            return true; // Nếu không có thông tin tuần thì áp dụng cho tất cả tuần
        }

        // Parse tuần học: "1-15", "1-8,10-15", "1,3,5,7"
        String[] ranges = tuanHoc.split(",");
        for (String range : ranges) {
            range = range.trim();
            if (range.contains("-")) {
                String[] parts = range.split("-");
                int start = Integer.parseInt(parts[0].trim());
                int end = Integer.parseInt(parts[1].trim());
                if (weekNumber >= start && weekNumber <= end) {
                    return true;
                }
            } else {
                int week = Integer.parseInt(range.trim());
                if (weekNumber == week) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Kiểm tra lịch trống của giảng viên
     */
    @Transactional(readOnly = true)
    public List<String> getAvailableTimeSlots(Long teacherId, Long semesterId, Integer dayOfWeek) {
        log.info("Getting available time slots for teacher: {}, semester: {}, day: {}", teacherId, semesterId, dayOfWeek);

        List<LichGiang> existingSchedules = lichGiangRepository.findByCanBoAndHocKy(teacherId, semesterId)
                .stream()
                .filter(s -> s.getThuHoc().equals(dayOfWeek))
                .collect(Collectors.toList());

        List<BuoiHoc> allSessions = buoiHocRepository.findAllOrderByTime();

        return allSessions.stream()
                .filter(session -> existingSchedules.stream()
                        .noneMatch(schedule -> schedule.getIdBuoi().equals(session.getIdBuoi())))
                .map(session -> session.getTenBuoi() + " (" + session.getGioBatDau() + " - " + session.getGioKetThuc() + ")")
                .collect(Collectors.toList());
    }

    /**
     * Copy lịch giảng từ học kỳ này sang học kỳ khác
     */
    public int copySchedulesToSemester(Long fromSemesterId, Long toSemesterId) {
        log.info("Copying schedules from semester {} to semester {}", fromSemesterId, toSemesterId);

        if (!authService.isAdmin() && !authService.isTruongKhoa()) {
            throw new BadRequestException("Bạn không có quyền thực hiện chức năng này");
        }

        // Kiểm tra học kỳ tồn tại
        hocKyRepository.findById(fromSemesterId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ nguồn"));
        hocKyRepository.findById(toSemesterId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ đích"));

        List<LichGiang> sourceSchedules = lichGiangRepository.findByIdHocKyAndTrangThaiTrue(fromSemesterId);
        int copiedCount = 0;

        for (LichGiang source : sourceSchedules) {
            try {
                // Kiểm tra xung đột trước khi copy
                ScheduleRequest request = createRequestFromEntity(source);
                request.setIdHocKy(toSemesterId);

                if (!checkScheduleConflict(request)) {
                    LichGiang newSchedule = new LichGiang();
                    mapRequestToEntity(request, newSchedule);
                    lichGiangRepository.save(newSchedule);
                    copiedCount++;
                }
            } catch (Exception e) {
                log.warn("Failed to copy schedule {}: {}", source.getIdLichGiang(), e.getMessage());
            }
        }

        log.info("Copied {} schedules from semester {} to semester {}", copiedCount, fromSemesterId, toSemesterId);
        return copiedCount;
    }

    private ScheduleRequest createRequestFromEntity(LichGiang entity) {
        ScheduleRequest request = new ScheduleRequest();
        request.setIdLop(entity.getIdLop());
        request.setIdHinhThuc(entity.getIdHinhThuc());
        request.setNhomTh(entity.getNhomTh());
        request.setIdMonHoc(entity.getIdMonHoc());
        request.setSoTiet(entity.getSoTiet());
        request.setHeSo(entity.getHeSo());
        request.setIdCanBo(entity.getIdCanBo());
        request.setThuHoc(entity.getThuHoc());
        request.setIdBuoi(entity.getIdBuoi());
        request.setIdPhong(entity.getIdPhong());
        request.setTuanHoc(entity.getTuanHoc());
        request.setGhiChu(entity.getGhiChu());
        request.setIdHocKy(entity.getIdHocKy());
        return request;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getScheduleCalendar(LocalDate startDate, LocalDate endDate, Long semesterId,
                                          Long teacherId, Long classId, Long departmentId) {
        log.info("Getting schedule calendar from {} to {}, semester: {}, teacher: {}, class: {}, department: {}",
                startDate, endDate, semesterId, teacherId, classId, departmentId);

        try {
            // Lấy tất cả lịch giảng theo điều kiện
            List<LichGiang> schedules = getSchedulesByFilters(semesterId, teacherId, classId, departmentId);

            // Chuyển đổi sang format calendar
            List<Object> calendarEvents = new ArrayList<>();

            for (LichGiang schedule : schedules) {
                // Tạo các sự kiện calendar cho từng tuần trong khoảng thời gian
                List<Object> events = createCalendarEventsForSchedule(schedule, startDate, endDate);
                calendarEvents.addAll(events);
            }

            // Sắp xếp theo ngày và giờ
            calendarEvents.sort((e1, e2) -> {
                Map<String, Object> event1 = (Map<String, Object>) e1;
                Map<String, Object> event2 = (Map<String, Object>) e2;
                String start1 = (String) event1.get("start");
                String start2 = (String) event2.get("start");
                return start1.compareTo(start2);
            });

            log.info("Generated {} calendar events", calendarEvents.size());
            return calendarEvents;

        } catch (Exception e) {
            log.error("Error getting schedule calendar: ", e);
            throw new RuntimeException("Lỗi khi lấy lịch giảng calendar: " + e.getMessage(), e);
        }
    }

    private List<LichGiang> getSchedulesByFilters(Long semesterId, Long teacherId, Long classId, Long departmentId) {
        List<LichGiang> schedules = new ArrayList<>();

        if (semesterId != null) {
            schedules = lichGiangRepository.findByIdHocKyAndTrangThaiTrue(semesterId);
        } else {
            schedules = lichGiangRepository.findByTrangThaiTrue();
        }

        // Lọc theo giảng viên
        if (teacherId != null) {
            schedules = schedules.stream()
                    .filter(s -> s.getIdCanBo().equals(teacherId))
                    .collect(Collectors.toList());
        }

        // Lọc theo lớp
        if (classId != null) {
            schedules = schedules.stream()
                    .filter(s -> s.getIdLop().equals(classId))
                    .collect(Collectors.toList());
        }

        // Lọc theo khoa
        if (departmentId != null) {
            schedules = schedules.stream()
                    .filter(s -> {
                        try {
                            CanBo teacher = canBoRepository.findById(s.getIdCanBo()).orElse(null);
                            return teacher != null && teacher.getIdKhoa().equals(departmentId);
                        } catch (Exception e) {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
        }

        // Lọc theo quyền truy cập
        return schedules.stream()
                .filter(this::canViewSchedule)
                .collect(Collectors.toList());
    }

    private List<Object> createCalendarEventsForSchedule(LichGiang schedule, LocalDate startDate, LocalDate endDate) {
        List<Object> events = new ArrayList<>();

        try {
            // Lấy thông tin chi tiết
            ScheduleResponse scheduleResponse = mapEntityToResponse(schedule);

            // Tính toán các ngày trong khoảng thời gian
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                // Kiểm tra xem ngày này có phải là thứ của lịch giảng không
                if (currentDate.getDayOfWeek().getValue() + 1 == schedule.getThuHoc()) {
                    // Kiểm tra tuần học (nếu có)
                    if (isDateInScheduleWeeks(currentDate, schedule.getTuanHoc(), startDate)) {
                        Map<String, Object> event = createCalendarEvent(schedule, scheduleResponse, currentDate);
                        events.add(event);
                    }
                }
                currentDate = currentDate.plusDays(1);
            }

        } catch (Exception e) {
            log.warn("Error creating calendar events for schedule {}: {}", schedule.getIdLichGiang(), e.getMessage());
        }

        return events;
    }

    private Map<String, Object> createCalendarEvent(LichGiang schedule, ScheduleResponse scheduleResponse, LocalDate date) {
        Map<String, Object> event = new HashMap<>();

        // Tạo ID unique cho mỗi event (scheduleId + date)
        String uniqueId = schedule.getIdLichGiang() + "_" + date.toString();
        event.put("id", uniqueId);
        event.put("title", createEventTitle(scheduleResponse));
        event.put("description", createEventDescription(scheduleResponse));

        // Thời gian - sử dụng thời gian từ BuoiHoc
        String startTime = scheduleResponse.getGioBatDau() != null ?
            scheduleResponse.getGioBatDau().toString() : "07:00:00";
        String endTime = scheduleResponse.getGioKetThuc() != null ?
            scheduleResponse.getGioKetThuc().toString() : "09:30:00";

        String startDateTime = date + "T" + startTime;
        String endDateTime = date + "T" + endTime;
        event.put("start", startDateTime);
        event.put("end", endDateTime);
        event.put("allDay", false);

        // Thông tin bổ sung
        event.put("backgroundColor", getEventColor(scheduleResponse.getHinhThucHoc()));
        event.put("borderColor", getEventColor(scheduleResponse.getHinhThucHoc()));
        event.put("textColor", "#ffffff");

        // Metadata
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("scheduleId", schedule.getIdLichGiang());
        extendedProps.put("teacherId", schedule.getIdCanBo());
        extendedProps.put("teacherName", scheduleResponse.getTenGiangVien());
        extendedProps.put("subjectCode", scheduleResponse.getMaMonHoc());
        extendedProps.put("subjectName", scheduleResponse.getTenMonHoc());
        extendedProps.put("classCode", scheduleResponse.getMaLop());
        extendedProps.put("className", scheduleResponse.getTenLop());
        extendedProps.put("roomName", scheduleResponse.getTenPhong());
        extendedProps.put("campusName", scheduleResponse.getTenCoSo());
        extendedProps.put("sessionName", scheduleResponse.getTenBuoi());
        extendedProps.put("formType", scheduleResponse.getHinhThucHoc());
        extendedProps.put("practiceGroup", scheduleResponse.getNhomTh());
        extendedProps.put("credits", scheduleResponse.getSoTiet());
        extendedProps.put("coefficient", scheduleResponse.getHeSo());
        extendedProps.put("note", scheduleResponse.getGhiChu());

        event.put("extendedProps", extendedProps);

        return event;
    }

    private String createEventTitle(ScheduleResponse schedule) {
        StringBuilder title = new StringBuilder();
        title.append(schedule.getMaMonHoc());
        if (schedule.getNhomTh() != null && !schedule.getNhomTh().trim().isEmpty()) {
            title.append(" (").append(schedule.getNhomTh()).append(")");
        }
        title.append(" - ").append(schedule.getTenPhong());
        return title.toString();
    }

    private String createEventDescription(ScheduleResponse schedule) {
        StringBuilder desc = new StringBuilder();
        desc.append("Môn: ").append(schedule.getTenMonHoc()).append("\n");
        desc.append("GV: ").append(schedule.getTenGiangVien()).append("\n");
        desc.append("Lớp: ").append(schedule.getTenLop()).append("\n");
        desc.append("Phòng: ").append(schedule.getTenPhong());
        if (schedule.getTenCoSo() != null) {
            desc.append(" (").append(schedule.getTenCoSo()).append(")");
        }
        desc.append("\n");
        desc.append("Buổi: ").append(schedule.getTenBuoi()).append("\n");
        desc.append("Hình thức: ").append(schedule.getHinhThucHoc()).append("\n");
        desc.append("Số tiết: ").append(schedule.getSoTiet());
        if (schedule.getGhiChu() != null && !schedule.getGhiChu().trim().isEmpty()) {
            desc.append("\nGhi chú: ").append(schedule.getGhiChu());
        }
        return desc.toString();
    }

    private String getEventColor(String formType) {
        if (formType == null) return "#3788d8";

        switch (formType.toUpperCase()) {
            case "LT":
            case "LÝ THUYẾT":
                return "#3788d8"; // Blue
            case "TH":
            case "THỰC HÀNH":
                return "#28a745"; // Green
            case "BT":
            case "BÀI TẬP":
                return "#ffc107"; // Yellow
            case "TN":
            case "THỰC NGHIỆM":
                return "#dc3545"; // Red
            case "DA":
            case "ĐỒ ÁN":
                return "#6f42c1"; // Purple
            default:
                return "#6c757d"; // Gray
        }
    }

    private boolean isDateInScheduleWeeks(LocalDate date, String tuanHoc, LocalDate semesterStart) {
        if (tuanHoc == null || tuanHoc.trim().isEmpty()) {
            return true; // Nếu không có thông tin tuần thì áp dụng cho tất cả tuần
        }

        // Tính tuần hiện tại (từ ngày bắt đầu học kỳ)
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(semesterStart, date);
        int currentWeek = (int) (daysBetween / 7) + 1;

        // Parse tuần học: "1-15", "1-8,10-15", "1,3,5,7"
        String[] ranges = tuanHoc.split(",");
        for (String range : ranges) {
            range = range.trim();
            if (range.contains("-")) {
                String[] parts = range.split("-");
                try {
                    int start = Integer.parseInt(parts[0].trim());
                    int end = Integer.parseInt(parts[1].trim());
                    if (currentWeek >= start && currentWeek <= end) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid week range format: {}", range);
                }
            } else {
                try {
                    int week = Integer.parseInt(range.trim());
                    if (currentWeek == week) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid week number format: {}", range);
                }
            }
        }

        return false;
    }
}
