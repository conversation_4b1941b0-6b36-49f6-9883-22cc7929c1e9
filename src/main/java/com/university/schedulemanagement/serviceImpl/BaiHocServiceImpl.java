package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.BaiHocRequest;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.entity.BaiHoc;
import com.university.schedulemanagement.entity.MonHoc;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.BaiHocRepository;
import com.university.schedulemanagement.repository.MonHocRepository;
import com.university.schedulemanagement.service.BaiHocService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * BaiHocServiceImpl - Implementation của BaiHocService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class BaiHocServiceImpl implements BaiHocService {

    private final BaiHocRepository baiHocRepository;
    private final MonHocRepository monHocRepository;

    // ==================== CRUD OPERATIONS ====================

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllBaiHoc(Pageable pageable) {
        log.info("Getting all bai hoc with pagination: page={}, size={}", pageable.getPageNumber(), pageable.getPageSize());
        
        try {
            Page<BaiHoc> baiHocPage = baiHocRepository.findAll(pageable);
            
            List<Object> responses = baiHocPage.getContent().stream()
                    .map(this::mapBaiHocToResponse)
                    .collect(Collectors.toList());
            
            return PageResponse.builder()
                    .content(responses)
                    .page(baiHocPage.getNumber())
                    .size(baiHocPage.getSize())
                    .totalElements(baiHocPage.getTotalElements())
                    .totalPages(baiHocPage.getTotalPages())
                    .first(baiHocPage.isFirst())
                    .last(baiHocPage.isLast())
                    .hasNext(baiHocPage.hasNext())
                    .hasPrevious(baiHocPage.hasPrevious())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error getting all bai hoc: ", e);
            throw new RuntimeException("Lỗi khi lấy danh sách bài học: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getBaiHocByMonHoc(Long idMonHoc, Pageable pageable) {
        log.info("Getting bai hoc by mon hoc: idMonHoc={}", idMonHoc);
        
        try {
            // Kiểm tra môn học tồn tại
            if (!monHocRepository.existsById(idMonHoc)) {
                throw new ResourceNotFoundException("Không tìm thấy môn học với ID: " + idMonHoc);
            }
            
            Page<BaiHoc> baiHocPage = baiHocRepository.findByIdMonHocWithPagination(idMonHoc, pageable);
            
            List<Object> responses = baiHocPage.getContent().stream()
                    .map(this::mapBaiHocToResponse)
                    .collect(Collectors.toList());
            
            return PageResponse.builder()
                    .content(responses)
                    .page(baiHocPage.getNumber())
                    .size(baiHocPage.getSize())
                    .totalElements(baiHocPage.getTotalElements())
                    .totalPages(baiHocPage.getTotalPages())
                    .first(baiHocPage.isFirst())
                    .last(baiHocPage.isLast())
                    .hasNext(baiHocPage.hasNext())
                    .hasPrevious(baiHocPage.hasPrevious())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error getting bai hoc by mon hoc {}: ", idMonHoc, e);
            throw new RuntimeException("Lỗi khi lấy danh sách bài học theo môn: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> getBaiHocByMonHocNoPaging(Long idMonHoc) {
        log.info("Getting bai hoc by mon hoc without pagination: idMonHoc={}", idMonHoc);
        
        try {
            // Kiểm tra môn học tồn tại
            if (!monHocRepository.existsById(idMonHoc)) {
                throw new ResourceNotFoundException("Không tìm thấy môn học với ID: " + idMonHoc);
            }
            
            List<BaiHoc> baiHocList = baiHocRepository.findByIdMonHocOrderBySttBaiHoc(idMonHoc);
            
            return baiHocList.stream()
                    .map(this::mapBaiHocToResponse)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting bai hoc by mon hoc no paging {}: ", idMonHoc, e);
            throw new RuntimeException("Lỗi khi lấy danh sách bài học: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> searchBaiHoc(String keyword, Pageable pageable) {
        log.info("Searching bai hoc with keyword: {}", keyword);
        
        try {
            Page<BaiHoc> baiHocPage = baiHocRepository.findByKeyword(keyword, pageable);
            
            List<Object> responses = baiHocPage.getContent().stream()
                    .map(this::mapBaiHocToResponse)
                    .collect(Collectors.toList());
            
            return PageResponse.builder()
                    .content(responses)
                    .page(baiHocPage.getNumber())
                    .size(baiHocPage.getSize())
                    .totalElements(baiHocPage.getTotalElements())
                    .totalPages(baiHocPage.getTotalPages())
                    .first(baiHocPage.isFirst())
                    .last(baiHocPage.isLast())
                    .hasNext(baiHocPage.hasNext())
                    .hasPrevious(baiHocPage.hasPrevious())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error searching bai hoc with keyword {}: ", keyword, e);
            throw new RuntimeException("Lỗi khi tìm kiếm bài học: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> searchBaiHocByMonHoc(Long idMonHoc, String keyword, Pageable pageable) {
        log.info("Searching bai hoc by mon hoc with keyword: idMonHoc={}, keyword={}", idMonHoc, keyword);
        
        try {
            // Kiểm tra môn học tồn tại
            if (!monHocRepository.existsById(idMonHoc)) {
                throw new ResourceNotFoundException("Không tìm thấy môn học với ID: " + idMonHoc);
            }
            
            Page<BaiHoc> baiHocPage = baiHocRepository.findByIdMonHocAndKeyword(idMonHoc, keyword, pageable);
            
            List<Object> responses = baiHocPage.getContent().stream()
                    .map(this::mapBaiHocToResponse)
                    .collect(Collectors.toList());
            
            return PageResponse.builder()
                    .content(responses)
                    .page(baiHocPage.getNumber())
                    .size(baiHocPage.getSize())
                    .totalElements(baiHocPage.getTotalElements())
                    .totalPages(baiHocPage.getTotalPages())
                    .first(baiHocPage.isFirst())
                    .last(baiHocPage.isLast())
                    .hasNext(baiHocPage.hasNext())
                    .hasPrevious(baiHocPage.hasPrevious())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error searching bai hoc by mon hoc {} with keyword {}: ", idMonHoc, keyword, e);
            throw new RuntimeException("Lỗi khi tìm kiếm bài học: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Object getBaiHocById(Long id) {
        log.info("Getting bai hoc by ID: {}", id);
        
        BaiHoc baiHoc = baiHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy bài học với ID: " + id));
        
        return mapBaiHocToDetailResponse(baiHoc);
    }

    @Override
    public Object createBaiHoc(BaiHocRequest request) {
        log.info("Creating new bai hoc: {}", request.getTenBaiHoc());
        
        // Validate dữ liệu
        validateBaiHocRequest(request, null);
        
        // Tạo entity mới
        BaiHoc baiHoc = new BaiHoc();
        mapBaiHocRequestToEntity(request, baiHoc);
        
        // Tự động tạo số thứ tự nếu chưa có
        if (baiHoc.getSttBaiHoc() == null) {
            baiHoc.setSttBaiHoc(generateNextSttForMonHoc(request.getIdMonHoc()));
        }
        
        // Lưu vào database
        baiHoc = baiHocRepository.save(baiHoc);
        
        log.info("Bai hoc created successfully with ID: {}", baiHoc.getIdBaiHoc());
        return mapBaiHocToResponse(baiHoc);
    }

    @Override
    public Object updateBaiHoc(Long id, BaiHocRequest request) {
        log.info("Updating bai hoc {}: {}", id, request.getTenBaiHoc());
        
        // Kiểm tra tồn tại
        BaiHoc existingBaiHoc = baiHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy bài học với ID: " + id));
        
        // Validate dữ liệu
        validateBaiHocRequest(request, id);
        
        // Cập nhật thông tin
        mapBaiHocRequestToEntity(request, existingBaiHoc);
        
        // Lưu vào database
        existingBaiHoc = baiHocRepository.save(existingBaiHoc);
        
        log.info("Bai hoc updated successfully: {}", id);
        return mapBaiHocToResponse(existingBaiHoc);
    }

    @Override
    public void deleteBaiHoc(Long id) {
        log.info("Deleting bai hoc: {}", id);
        
        // Kiểm tra tồn tại
        BaiHoc baiHoc = baiHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy bài học với ID: " + id));
        
        // Xóa bài học
        baiHocRepository.delete(baiHoc);
        
        log.info("Bai hoc deleted successfully: {}", id);
    }

    @Override
    public Object toggleBaiHocStatus(Long id) {
        log.info("Toggling bai hoc status: {}", id);
        
        BaiHoc baiHoc = baiHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy bài học với ID: " + id));
        
        // Đổi trạng thái
        baiHoc.setTrangThai(!baiHoc.getTrangThai());
        baiHoc = baiHocRepository.save(baiHoc);
        
        log.info("Bai hoc status toggled successfully: {} -> {}", id, baiHoc.getTrangThai());
        return mapBaiHocToResponse(baiHoc);
    }

    // ==================== PRIVATE HELPER METHODS ====================
    
    private void validateBaiHocRequest(BaiHocRequest request, Long excludeId) {
        // Kiểm tra môn học tồn tại
        monHocRepository.findById(request.getIdMonHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + request.getIdMonHoc()));
        
        // Kiểm tra trùng mã bài học
        if (excludeId == null) {
            if (baiHocRepository.existsByMaBaiHoc(request.getMaBaiHoc())) {
                throw new BadRequestException("Mã bài học đã tồn tại: " + request.getMaBaiHoc());
            }
        } else {
            if (baiHocRepository.existsByMaBaiHocAndIdBaiHocNot(request.getMaBaiHoc(), excludeId)) {
                throw new BadRequestException("Mã bài học đã tồn tại: " + request.getMaBaiHoc());
            }
        }
        
        // Kiểm tra trùng số thứ tự trong môn học
        if (request.getSttBaiHoc() != null) {
            if (excludeId == null) {
                if (baiHocRepository.existsByIdMonHocAndSttBaiHoc(request.getIdMonHoc(), request.getSttBaiHoc())) {
                    throw new BadRequestException("Số thứ tự bài học đã tồn tại trong môn học: " + request.getSttBaiHoc());
                }
            } else {
                if (baiHocRepository.existsByIdMonHocAndSttBaiHocAndIdBaiHocNot(request.getIdMonHoc(), request.getSttBaiHoc(), excludeId)) {
                    throw new BadRequestException("Số thứ tự bài học đã tồn tại trong môn học: " + request.getSttBaiHoc());
                }
            }
        }
        
        // Kiểm tra có ít nhất 1 loại tiết học
        if (!request.hasValidSoTiet()) {
            throw new BadRequestException("Bài học phải có ít nhất 1 tiết (lý thuyết, thực hành hoặc tự học)");
        }
    }

    private void mapBaiHocRequestToEntity(BaiHocRequest request, BaiHoc baiHoc) {
        baiHoc.setIdMonHoc(request.getIdMonHoc());
        baiHoc.setMaBaiHoc(request.getMaBaiHoc());
        baiHoc.setTenBaiHoc(request.getTenBaiHoc());
        baiHoc.setSttBaiHoc(request.getSttBaiHoc());
        baiHoc.setSoTietLt(request.getSoTietLt());
        baiHoc.setSoTietTh(request.getSoTietTh());
        baiHoc.setSoTietTu(request.getSoTietTu());
        baiHoc.setMucTieu(request.getMucTieu());
        baiHoc.setNoiDung(request.getNoiDung());
        baiHoc.setPhuongPhapDay(request.getPhuongPhapDay());
        baiHoc.setTaiLieuThamKhao(request.getTaiLieuThamKhao());
        baiHoc.setBaiTap(request.getBaiTap());
        baiHoc.setDanhGia(request.getDanhGia());
        baiHoc.setGhiChu(request.getGhiChu());
        baiHoc.setTrangThai(request.getTrangThai());
    }

    private Object mapBaiHocToResponse(BaiHoc baiHoc) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("idBaiHoc", baiHoc.getIdBaiHoc());
            response.put("idMonHoc", baiHoc.getIdMonHoc());
            response.put("maBaiHoc", baiHoc.getMaBaiHoc());
            response.put("tenBaiHoc", baiHoc.getTenBaiHoc());
            response.put("sttBaiHoc", baiHoc.getSttBaiHoc());
            response.put("soTietLt", baiHoc.getSoTietLt());
            response.put("soTietTh", baiHoc.getSoTietTh());
            response.put("soTietTu", baiHoc.getSoTietTu());
            response.put("tongSoTiet", baiHoc.getTongSoTiet());
            response.put("trangThai", baiHoc.getTrangThai());
            response.put("ngayTao", baiHoc.getNgayTao());
            response.put("ngayCapNhat", baiHoc.getNgayCapNhat());
            
            // Thêm thông tin môn học nếu có
            try {
                if (baiHoc.getMonHoc() != null) {
                    response.put("tenMonHoc", baiHoc.getMonHoc().getTenMonHoc());
                    response.put("maMonHoc", baiHoc.getMonHoc().getMaMonHoc());
                } else {
                    // Lấy thông tin môn học từ repository nếu cần
                    MonHoc monHoc = monHocRepository.findById(baiHoc.getIdMonHoc()).orElse(null);
                    if (monHoc != null) {
                        response.put("tenMonHoc", monHoc.getTenMonHoc());
                        response.put("maMonHoc", monHoc.getMaMonHoc());
                    }
                }
            } catch (Exception e) {
                log.warn("Could not load mon hoc info for bai hoc {}: {}", baiHoc.getIdBaiHoc(), e.getMessage());
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error mapping BaiHoc to response for ID {}: {}", baiHoc.getIdBaiHoc(), e.getMessage());
            // Return basic info nếu có lỗi
            Map<String, Object> basicResponse = new HashMap<>();
            basicResponse.put("idBaiHoc", baiHoc.getIdBaiHoc());
            basicResponse.put("maBaiHoc", baiHoc.getMaBaiHoc());
            basicResponse.put("tenBaiHoc", baiHoc.getTenBaiHoc());
            basicResponse.put("sttBaiHoc", baiHoc.getSttBaiHoc());
            return basicResponse;
        }
    }

    private Object mapBaiHocToDetailResponse(BaiHoc baiHoc) {
        Map<String, Object> response = (Map<String, Object>) mapBaiHocToResponse(baiHoc);
        
        // Thêm thông tin chi tiết
        response.put("mucTieu", baiHoc.getMucTieu());
        response.put("noiDung", baiHoc.getNoiDung());
        response.put("phuongPhapDay", baiHoc.getPhuongPhapDay());
        response.put("taiLieuThamKhao", baiHoc.getTaiLieuThamKhao());
        response.put("baiTap", baiHoc.getBaiTap());
        response.put("danhGia", baiHoc.getDanhGia());
        response.put("ghiChu", baiHoc.getGhiChu());
        
        return response;
    }

    // ==================== IMPLEMENTATION OF REMAINING METHODS ====================
    
    @Override
    public Integer generateNextSttForMonHoc(Long idMonHoc) {
        Integer maxStt = baiHocRepository.findMaxSttBaiHocByIdMonHoc(idMonHoc);
        return maxStt != null ? maxStt + 1 : 1;
    }

    @Override
    public boolean existsByMaBaiHoc(String maBaiHoc) {
        return baiHocRepository.existsByMaBaiHoc(maBaiHoc);
    }

    @Override
    public boolean existsByMaBaiHocAndNotId(String maBaiHoc, Long excludeId) {
        return baiHocRepository.existsByMaBaiHocAndIdBaiHocNot(maBaiHoc, excludeId);
    }

    @Override
    public boolean existsBySttInMonHoc(Long idMonHoc, Integer sttBaiHoc) {
        return baiHocRepository.existsByIdMonHocAndSttBaiHoc(idMonHoc, sttBaiHoc);
    }

    @Override
    public boolean existsBySttInMonHocAndNotId(Long idMonHoc, Integer sttBaiHoc, Long excludeId) {
        return baiHocRepository.existsByIdMonHocAndSttBaiHocAndIdBaiHocNot(idMonHoc, sttBaiHoc, excludeId);
    }

    @Override
    public Integer getTotalHoursByMonHoc(Long idMonHoc) {
        return baiHocRepository.sumTotalHoursByIdMonHoc(idMonHoc);
    }

    // TODO: Implement remaining methods (copyBaiHoc, reorderBaiHoc, moveBaiHoc, etc.)
    // These methods will be implemented in the next part due to file size limit
    
    @Override
    public Object copyBaiHoc(Long id, BaiHocRequest request) {
        throw new UnsupportedOperationException("Chức năng sao chép bài học đang được phát triển");
    }

    @Override
    public void reorderBaiHoc(Long idMonHoc, List<Long> orderedIds) {
        throw new UnsupportedOperationException("Chức năng sắp xếp lại bài học đang được phát triển");
    }

    @Override
    public Object moveBaiHoc(Long id, String direction) {
        throw new UnsupportedOperationException("Chức năng di chuyển bài học đang được phát triển");
    }

    @Override
    public Object getNextBaiHoc(Long idMonHoc, Integer currentStt) {
        throw new UnsupportedOperationException("Chức năng lấy bài học tiếp theo đang được phát triển");
    }

    @Override
    public Object getPreviousBaiHoc(Long idMonHoc, Integer currentStt) {
        throw new UnsupportedOperationException("Chức năng lấy bài học trước đó đang được phát triển");
    }

    @Override
    public Object getBaiHocStatistics(Long idMonHoc) {
        throw new UnsupportedOperationException("Chức năng thống kê bài học đang được phát triển");
    }

    @Override
    public Object getBaiHocOverallStatistics() {
        throw new UnsupportedOperationException("Chức năng thống kê tổng quan đang được phát triển");
    }

    @Override
    public String generateMaBaiHoc(Long idMonHoc, Integer sttBaiHoc) {
        throw new UnsupportedOperationException("Chức năng tự động tạo mã bài học đang được phát triển");
    }

    @Override
    public byte[] exportBaiHocToExcel(Long idMonHoc) {
        throw new UnsupportedOperationException("Chức năng xuất Excel đang được phát triển");
    }

    @Override
    public Object importBaiHocFromExcel(Long idMonHoc, byte[] excelData) {
        throw new UnsupportedOperationException("Chức năng import Excel đang được phát triển");
    }
}
