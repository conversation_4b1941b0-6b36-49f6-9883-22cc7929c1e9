package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.DepartmentRequest;
import com.university.schedulemanagement.dto.request.SubjectRequest;
import com.university.schedulemanagement.entity.Khoa;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.repository.KhoaRepository;
import com.university.schedulemanagement.repository.MonHocRepository;
import com.university.schedulemanagement.service.ExcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * ExcelServiceImpl - Implementation của ExcelService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ExcelServiceImpl implements ExcelService {

    private final KhoaRepository khoaRepository;
    private final MonHocRepository monHocRepository;

    @Override
    public <T> List<T> importFromExcel(MultipartFile file, Class<T> targetClass, String sheetName) {
        log.info("Importing data from Excel file: {}, sheet: {}", file.getOriginalFilename(), sheetName);
        
        List<T> result = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new BadRequestException("Không tìm thấy sheet: " + sheetName);
            }
            
            // Process rows (skip header row)
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null && !isRowEmpty(row)) {
                    T item = processRow(row, targetClass);
                    if (item != null) {
                        result.add(item);
                    }
                }
            }
            
        } catch (IOException e) {
            log.error("Error reading Excel file: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi đọc file Excel: " + e.getMessage());
        }
        
        log.info("Imported {} records from Excel", result.size());
        return result;
    }

    @Override
    public <T> byte[] exportToExcel(List<T> data, Class<T> dataClass, String sheetName, String fileName) {
        log.info("Exporting {} records to Excel", data.size());
        
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet(sheetName);
            
            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            
            // Create header row
            createHeaderRow(sheet, dataClass, headerStyle);
            
            // Create data rows
            for (int i = 0; i < data.size(); i++) {
                createDataRow(sheet, i + 1, data.get(i), dataClass);
            }
            
            // Auto-size columns
            autoSizeColumns(sheet, getColumnCount(dataClass));
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("Error creating Excel file: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi tạo file Excel: " + e.getMessage());
        }
    }

    @Override
    public byte[] createTemplate(String dataType, String fileName) {
        log.info("Creating template for data type: {}", dataType);
        
        switch (dataType.toLowerCase()) {
            case "department":
            case "khoa":
                return exportDepartmentTemplate();
            case "subject":
            case "monhoc":
                return exportSubjectTemplate();
            case "class":
            case "lophoc":
                return exportClassTemplate();
            case "room":
            case "phonghoc":
                return exportRoomTemplate();
            case "teacher":
            case "giangvien":
                return exportTeacherTemplate();
            default:
                throw new BadRequestException("Loại dữ liệu không được hỗ trợ: " + dataType);
        }
    }

    @Override
    public <T> Map<String, Object> validateImportData(List<T> data, Class<T> dataClass) {
        log.info("Validating {} records of type {}", data.size(), dataClass.getSimpleName());
        
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // Validate each record
        for (int i = 0; i < data.size(); i++) {
            T item = data.get(i);
            List<String> itemErrors = validateItem(item, i + 2); // +2 for header row and 0-based index
            errors.addAll(itemErrors);
        }
        
        result.put("totalRecords", data.size());
        result.put("validRecords", data.size() - errors.size());
        result.put("errors", errors);
        result.put("warnings", warnings);
        result.put("isValid", errors.isEmpty());
        
        return result;
    }

    @Override
    public Map<String, Object> importDepartments(MultipartFile file) {
        log.info("Importing departments from Excel file: {}", file.getOriginalFilename());
        
        try {
            List<DepartmentRequest> departments = importFromExcel(file, DepartmentRequest.class, "Departments");
            
            // Validate data
            Map<String, Object> validation = validateImportData(departments, DepartmentRequest.class);
            if (!(Boolean) validation.get("isValid")) {
                return validation;
            }
            
            // Save to database
            int savedCount = 0;
            List<String> errors = new ArrayList<>();
            
            for (DepartmentRequest dept : departments) {
                try {
                    if (!khoaRepository.existsByMaKhoa(dept.getMaKhoa())) {
                        Khoa khoa = new Khoa();
                        khoa.setMaKhoa(dept.getMaKhoa());
                        khoa.setTenKhoa(dept.getTenKhoa());
                        khoa.setTrangThai(dept.getTrangThai());
                        khoaRepository.save(khoa);
                        savedCount++;
                    } else {
                        errors.add("Mã khoa đã tồn tại: " + dept.getMaKhoa());
                    }
                } catch (Exception e) {
                    errors.add("Lỗi khi lưu khoa " + dept.getMaKhoa() + ": " + e.getMessage());
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalRecords", departments.size());
            result.put("savedRecords", savedCount);
            result.put("errors", errors);
            result.put("success", errors.isEmpty());
            
            return result;
            
        } catch (Exception e) {
            log.error("Error importing departments: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi import dữ liệu khoa: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> importSubjects(MultipartFile file) {
        log.info("Importing subjects from Excel file: {}", file.getOriginalFilename());
        
        try {
            List<SubjectRequest> subjects = importFromExcel(file, SubjectRequest.class, "Subjects");
            
            // Validate and save logic similar to importDepartments
            // TODO: Implement full logic
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "Import subjects - chưa triển khai đầy đủ");
            return result;
            
        } catch (Exception e) {
            log.error("Error importing subjects: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi import dữ liệu môn học: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportDepartmentTemplate() {
        log.info("Creating department template");
        
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("Departments");
            
            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {"Mã Khoa", "Tên Khoa", "Mô Tả", "Địa Chỉ", "Số Điện Thoại", "Email", "Trạng Thái"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create sample data row
            Row sampleRow = sheet.createRow(1);
            sampleRow.createCell(0).setCellValue("CNTT");
            sampleRow.createCell(1).setCellValue("Công nghệ thông tin");
            sampleRow.createCell(2).setCellValue("Khoa Công nghệ thông tin");
            sampleRow.createCell(3).setCellValue("123 Đường ABC, Quận 1, TP.HCM");
            sampleRow.createCell(4).setCellValue("028-12345678");
            sampleRow.createCell(5).setCellValue("<EMAIL>");
            sampleRow.createCell(6).setCellValue("true");
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("Error creating department template: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi tạo template khoa: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportSubjectTemplate() {
        log.info("Creating subject template");
        
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("Subjects");
            
            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {"Mã Môn Học", "Tên Môn Học", "ID Khoa", "ID Loại Môn Học", "ID Hệ Đào Tạo", 
                               "Số Tiết LT", "Số Tiết TH", "Số Tiết Tự Học", "Môn Điều Kiện", "Môn TN"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // Create sample data row
            Row sampleRow = sheet.createRow(1);
            sampleRow.createCell(0).setCellValue("CNTT101");
            sampleRow.createCell(1).setCellValue("Nhập môn Công nghệ thông tin");
            sampleRow.createCell(2).setCellValue("1");
            sampleRow.createCell(3).setCellValue("1");
            sampleRow.createCell(4).setCellValue("1");
            sampleRow.createCell(5).setCellValue("30");
            sampleRow.createCell(6).setCellValue("15");
            sampleRow.createCell(7).setCellValue("45");
            sampleRow.createCell(8).setCellValue("false");
            sampleRow.createCell(9).setCellValue("false");
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("Error creating subject template: {}", e.getMessage());
            throw new BadRequestException("Lỗi khi tạo template môn học: " + e.getMessage());
        }
    }

    // ==================== PRIVATE HELPER METHODS ====================
    
    private boolean isRowEmpty(Row row) {
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String cellValue = cell.toString().trim();
                if (!cellValue.isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    private <T> T processRow(Row row, Class<T> targetClass) {
        // TODO: Implement generic row processing based on class type
        // For now, return null - specific implementations needed for each class
        return null;
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        return style;
    }

    private <T> void createHeaderRow(Sheet sheet, Class<T> dataClass, CellStyle headerStyle) {
        // TODO: Implement based on class annotations or reflection
    }

    private <T> void createDataRow(Sheet sheet, int rowIndex, T data, Class<T> dataClass) {
        // TODO: Implement based on class fields
    }

    private <T> int getColumnCount(Class<T> dataClass) {
        // TODO: Implement based on class fields
        return 5; // Default
    }

    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private <T> List<String> validateItem(T item, int rowNumber) {
        List<String> errors = new ArrayList<>();
        // TODO: Implement validation based on item type
        return errors;
    }

    // ==================== CHƯA TRIỂN KHAI ĐẦY ĐỦ ====================
    
    @Override
    public Map<String, Object> importClasses(MultipartFile file) {
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Map<String, Object> importRooms(MultipartFile file) {
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Map<String, Object> importTeachers(MultipartFile file) {
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public byte[] exportClassTemplate() {
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public byte[] exportRoomTemplate() {
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public byte[] exportTeacherTemplate() {
        throw new UnsupportedOperationException("Chưa triển khai");
    }
}
