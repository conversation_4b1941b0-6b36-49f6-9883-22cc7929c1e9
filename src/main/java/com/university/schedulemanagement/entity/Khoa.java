package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
/**
 * Khoa Entity - Phòng Ban, Môn, Module (Khoa)
 */
@Entity
@Table(name = "Khoa")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Khoa extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_KHOA")
    private Long idKhoa;

    @Column(name = "MA_KHOA", nullable = false, length = 10)
    private String maKhoa;

    @Column(name = "TEN_KHOA", nullable = false, length = 250)
    private String tenKhoa;

    @OneToMany(mappedBy = "khoa", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<CanBo> canBoList;

    @OneToMany(mappedBy = "khoa", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<MonHoc> monHocList;
}
