package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * BaiHoc Entity - <PERSON><PERSON>i học
 */
@Entity
@Table(name = "BAI_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BaiHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_BAI_HOC")
    private Long idBaiHoc;

    @Column(name = "ID_MON_HOC", nullable = false)
    private Long idMonHoc;

    @Column(name = "MA_BAI_HOC", nullable = false, length = 20)
    private String maBaiHoc;

    @Column(name = "TEN_BAI_HOC", nullable = false, length = 250)
    private String tenBaiHoc;

    @Column(name = "STT_BAI_HOC")
    private Integer sttBaiHoc; // Số thứ tự bài học trong môn

    @Column(name = "SO_TIET_LT")
    private Integer soTietLt = 0; // Số tiết lý thuyết

    @Column(name = "SO_TIET_TH")
    private Integer soTietTh = 0; // Số tiết thực hành

    @Column(name = "SO_TIET_TU")
    private Integer soTietTu = 0; // Số tiết tự học

    @Column(name = "MUC_TIEU", columnDefinition = "TEXT")
    private String mucTieu; // Mục tiêu bài học

    @Column(name = "NOI_DUNG", columnDefinition = "TEXT")
    private String noiDung; // Nội dung chi tiết

    @Column(name = "PHUONG_PHAP_DAY", columnDefinition = "TEXT")
    private String phuongPhapDay; // Phương pháp giảng dạy

    @Column(name = "TAI_LIEU_THAM_KHAO", columnDefinition = "TEXT")
    private String taiLieuThamKhao; // Tài liệu tham khảo

    @Column(name = "BAI_TAP", columnDefinition = "TEXT")
    private String baiTap; // Bài tập về nhà

    @Column(name = "DANH_GIA", columnDefinition = "TEXT")
    private String danhGia; // Phương pháp đánh giá

    @Column(name = "GHI_CHU", columnDefinition = "TEXT")
    private String ghiChu; // Ghi chú thêm

    @Column(name = "TRANG_THAI")
    private Boolean trangThai = true; // Trạng thái hoạt động

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_MON_HOC", insertable = false, updatable = false)
    @JsonIgnore
    private MonHoc monHoc;

    // Helper methods
    public String getBaiHocInfo() {
        return String.format("Bài %d: %s - %s", 
            sttBaiHoc != null ? sttBaiHoc : 0, 
            maBaiHoc, 
            tenBaiHoc);
    }

    public Integer getTongSoTiet() {
        return (soTietLt != null ? soTietLt : 0) +
               (soTietTh != null ? soTietTh : 0) +
               (soTietTu != null ? soTietTu : 0);
    }

    public Boolean hasThucHanh() {
        return soTietTh != null && soTietTh > 0;
    }

    public Boolean hasLyThuyet() {
        return soTietLt != null && soTietLt > 0;
    }

    public Boolean hasTuHoc() {
        return soTietTu != null && soTietTu > 0;
    }

    public String getTrangThaiText() {
        return trangThai != null && trangThai ? "Hoạt động" : "Không hoạt động";
    }

    public String getFullInfo() {
        return String.format("%s - %s (%s)", 
            maBaiHoc, 
            tenBaiHoc,
            monHoc != null ? monHoc.getTenMonHoc() : "");
    }
}
