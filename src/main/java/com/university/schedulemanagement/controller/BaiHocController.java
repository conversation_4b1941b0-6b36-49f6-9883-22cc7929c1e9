package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.BaiHocRequest;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.service.BaiHocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * BaiHocController - REST Controller cho quản lý bài học
 */
@RestController
@RequestMapping("/api/bai-hoc")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Bài Học", description = "API quản lý bài học")
public class BaiHocController {

    private final BaiHocService baiHocService;

    // ==================== CRUD OPERATIONS ====================

    @GetMapping
    @Operation(summary = "Lấy danh sách bài học", description = "Lấy danh sách tất cả bài học với phân trang và sắp xếp")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllBaiHoc(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sttBaiHoc") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            log.info("Getting all bai hoc");
            
            // Validate sort field for BaiHoc entity
            String validSortBy = validateAndGetSortField(sortBy, "sttBaiHoc", 
                "idBaiHoc", "maBaiHoc", "tenBaiHoc", "sttBaiHoc", "soTietLt", "soTietTh", "soTietTu", "trangThai", "ngayTao", "ngayCapNhat");
            
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(validSortBy).descending() : Sort.by(validSortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            PageResponse<Object> response = baiHocService.getAllBaiHoc(pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách bài học thành công"));
        } catch (Exception e) {
            log.error("Error getting all bai hoc: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách bài học: " + e.getMessage()));
        }
    }

    @GetMapping("/mon-hoc/{idMonHoc}")
    @Operation(summary = "Lấy bài học theo môn học", description = "Lấy danh sách bài học của một môn học cụ thể")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getBaiHocByMonHoc(
            @Parameter(description = "ID môn học") @PathVariable Long idMonHoc,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sttBaiHoc") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            log.info("Getting bai hoc by mon hoc: {}", idMonHoc);
            
            String validSortBy = validateAndGetSortField(sortBy, "sttBaiHoc", 
                "idBaiHoc", "maBaiHoc", "tenBaiHoc", "sttBaiHoc", "soTietLt", "soTietTh", "soTietTu", "trangThai", "ngayTao", "ngayCapNhat");
            
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(validSortBy).descending() : Sort.by(validSortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            PageResponse<Object> response = baiHocService.getBaiHocByMonHoc(idMonHoc, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách bài học theo môn thành công"));
        } catch (Exception e) {
            log.error("Error getting bai hoc by mon hoc {}: {}", idMonHoc, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách bài học theo môn: " + e.getMessage()));
        }
    }

    @GetMapping("/mon-hoc/{idMonHoc}/all")
    @Operation(summary = "Lấy tất cả bài học theo môn (không phân trang)", description = "Lấy danh sách tất cả bài học của môn học không phân trang")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<List<Object>>> getBaiHocByMonHocNoPaging(
            @Parameter(description = "ID môn học") @PathVariable Long idMonHoc) {
        try {
            log.info("Getting all bai hoc by mon hoc without pagination: {}", idMonHoc);
            
            List<Object> response = baiHocService.getBaiHocByMonHocNoPaging(idMonHoc);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách bài học thành công"));
        } catch (Exception e) {
            log.error("Error getting all bai hoc by mon hoc {}: {}", idMonHoc, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách bài học: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    @Operation(summary = "Tìm kiếm bài học", description = "Tìm kiếm bài học theo từ khóa")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> searchBaiHoc(
            @Parameter(description = "Từ khóa tìm kiếm") @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sttBaiHoc") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            log.info("Searching bai hoc with keyword: {}", keyword);
            
            String validSortBy = validateAndGetSortField(sortBy, "sttBaiHoc", 
                "idBaiHoc", "maBaiHoc", "tenBaiHoc", "sttBaiHoc", "soTietLt", "soTietTh", "soTietTu", "trangThai", "ngayTao", "ngayCapNhat");
            
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(validSortBy).descending() : Sort.by(validSortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            PageResponse<Object> response = baiHocService.searchBaiHoc(keyword, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Tìm kiếm bài học thành công"));
        } catch (Exception e) {
            log.error("Error searching bai hoc with keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tìm kiếm bài học: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Lấy thông tin bài học", description = "Lấy thông tin chi tiết của một bài học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Object>> getBaiHocById(
            @Parameter(description = "ID bài học") @PathVariable Long id) {
        try {
            log.info("Getting bai hoc by ID: {}", id);
            
            Object response = baiHocService.getBaiHocById(id);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy thông tin bài học thành công"));
        } catch (Exception e) {
            log.error("Error getting bai hoc by ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thông tin bài học: " + e.getMessage()));
        }
    }

    @PostMapping
    @Operation(summary = "Tạo bài học mới", description = "Tạo một bài học mới")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Object>> createBaiHoc(
            @Parameter(description = "Thông tin bài học") @Valid @RequestBody BaiHocRequest request) {
        try {
            log.info("Creating new bai hoc: {}", request.getTenBaiHoc());
            
            Object response = baiHocService.createBaiHoc(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo bài học thành công"));
        } catch (Exception e) {
            log.error("Error creating bai hoc: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo bài học: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Cập nhật bài học", description = "Cập nhật thông tin bài học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Object>> updateBaiHoc(
            @Parameter(description = "ID bài học") @PathVariable Long id,
            @Parameter(description = "Thông tin bài học") @Valid @RequestBody BaiHocRequest request) {
        try {
            log.info("Updating bai hoc {}: {}", id, request.getTenBaiHoc());
            
            Object response = baiHocService.updateBaiHoc(id, request);
            return ResponseEntity.ok(ApiResponse.success(response, "Cập nhật bài học thành công"));
        } catch (Exception e) {
            log.error("Error updating bai hoc {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi cập nhật bài học: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Xóa bài học", description = "Xóa một bài học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<Void>> deleteBaiHoc(
            @Parameter(description = "ID bài học") @PathVariable Long id) {
        try {
            log.info("Deleting bai hoc: {}", id);
            
            baiHocService.deleteBaiHoc(id);
            return ResponseEntity.ok(ApiResponse.success(null, "Xóa bài học thành công"));
        } catch (Exception e) {
            log.error("Error deleting bai hoc {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi xóa bài học: " + e.getMessage()));
        }
    }

    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "Thay đổi trạng thái bài học", description = "Kích hoạt/vô hiệu hóa bài học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<Object>> toggleBaiHocStatus(
            @Parameter(description = "ID bài học") @PathVariable Long id) {
        try {
            log.info("Toggling bai hoc status: {}", id);
            
            Object response = baiHocService.toggleBaiHocStatus(id);
            return ResponseEntity.ok(ApiResponse.success(response, "Thay đổi trạng thái bài học thành công"));
        } catch (Exception e) {
            log.error("Error toggling bai hoc status {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi thay đổi trạng thái bài học: " + e.getMessage()));
        }
    }

    // ==================== UTILITY METHODS ====================

    @GetMapping("/statistics/mon-hoc/{idMonHoc}")
    @Operation(summary = "Thống kê bài học theo môn", description = "Lấy thống kê bài học của một môn học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Object>> getBaiHocStatistics(
            @Parameter(description = "ID môn học") @PathVariable Long idMonHoc) {
        try {
            log.info("Getting bai hoc statistics for mon hoc: {}", idMonHoc);
            
            Object response = baiHocService.getBaiHocStatistics(idMonHoc);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy thống kê bài học thành công"));
        } catch (Exception e) {
            log.error("Error getting bai hoc statistics for mon hoc {}: {}", idMonHoc, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thống kê bài học: " + e.getMessage()));
        }
    }

    @GetMapping("/generate-stt/{idMonHoc}")
    @Operation(summary = "Tạo số thứ tự tiếp theo", description = "Tạo số thứ tự bài học tiếp theo cho môn học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<Integer>> generateNextStt(
            @Parameter(description = "ID môn học") @PathVariable Long idMonHoc) {
        try {
            log.info("Generating next STT for mon hoc: {}", idMonHoc);
            
            Integer nextStt = baiHocService.generateNextSttForMonHoc(idMonHoc);
            return ResponseEntity.ok(ApiResponse.success(nextStt, "Tạo số thứ tự thành công"));
        } catch (Exception e) {
            log.error("Error generating next STT for mon hoc {}: {}", idMonHoc, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo số thứ tự: " + e.getMessage()));
        }
    }

    // ==================== HELPER METHODS ====================
    
    /**
     * Validate and get valid sort field
     */
    private String validateAndGetSortField(String requestedSort, String defaultSort, String... validFields) {
        if (requestedSort == null || requestedSort.trim().isEmpty()) {
            return defaultSort;
        }
        
        List<String> validFieldsList = Arrays.asList(validFields);
        if (validFieldsList.contains(requestedSort)) {
            return requestedSort;
        }
        
        log.warn("Invalid sort field requested: {}. Using default: {}", requestedSort, defaultSort);
        return defaultSort;
    }
}
