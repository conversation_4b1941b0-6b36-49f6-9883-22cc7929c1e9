package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

/**
 * BaiHocRequest - DTO cho request tạo/cập nhật bài học
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaiHocRequest {

    @NotNull(message = "Phải chọn môn học")
    private Long idMonHoc;

    @NotBlank(message = "Mã bài học không được để trống")
    @Size(max = 20, message = "Mã bài học không được vượt quá 20 ký tự")
    private String maBaiHoc;

    @NotBlank(message = "Tên bài học không được để trống")
    @Size(max = 250, message = "Tên bài học không được vượt quá 250 ký tự")
    private String tenBaiHoc;

    @Min(value = 1, message = "Số thứ tự bài học phải lớn hơn 0")
    private Integer sttBaiHoc;

    @Min(value = 0, message = "Số tiết lý thuyết không được âm")
    private Integer soTietLt = 0;

    @Min(value = 0, message = "Số tiết thực hành không được âm")
    private Integer soTietTh = 0;

    @Min(value = 0, message = "Số tiết tự học không được âm")
    private Integer soTietTu = 0;

    @Size(max = 1000, message = "Mục tiêu không được vượt quá 1000 ký tự")
    private String mucTieu;

    @Size(max = 2000, message = "Nội dung không được vượt quá 2000 ký tự")
    private String noiDung;

    @Size(max = 1000, message = "Phương pháp dạy không được vượt quá 1000 ký tự")
    private String phuongPhapDay;

    @Size(max = 1000, message = "Tài liệu tham khảo không được vượt quá 1000 ký tự")
    private String taiLieuThamKhao;

    @Size(max = 1000, message = "Bài tập không được vượt quá 1000 ký tự")
    private String baiTap;

    @Size(max = 1000, message = "Đánh giá không được vượt quá 1000 ký tự")
    private String danhGia;

    @Size(max = 500, message = "Ghi chú không được vượt quá 500 ký tự")
    private String ghiChu;

    private Boolean trangThai = true;

    // Validation methods
    public boolean hasValidSoTiet() {
        return (soTietLt != null && soTietLt > 0) || 
               (soTietTh != null && soTietTh > 0) || 
               (soTietTu != null && soTietTu > 0);
    }

    public Integer getTongSoTiet() {
        return (soTietLt != null ? soTietLt : 0) +
               (soTietTh != null ? soTietTh : 0) +
               (soTietTu != null ? soTietTu : 0);
    }
}
