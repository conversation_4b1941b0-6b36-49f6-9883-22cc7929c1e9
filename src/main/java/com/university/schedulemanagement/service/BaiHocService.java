package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.BaiHocRequest;
import com.university.schedulemanagement.dto.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * BaiHocService - Service interface cho quản lý bài học
 */
public interface BaiHocService {

    // ==================== CRUD OPERATIONS ====================

    /**
     * L<PERSON>y danh sách tất cả bài học với phân trang
     */
    PageResponse<Object> getAllBaiHoc(Pageable pageable);

    /**
     * L<PERSON>y danh sách bài học theo môn học
     */
    PageResponse<Object> getBaiHocByMonHoc(Long idMonHoc, Pageable pageable);

    /**
     * Lấy danh sách bài học theo môn học (không phân trang)
     */
    List<Object> getBaiHocByMonHocNoPaging(Long idMonHoc);

    /**
     * Tìm kiếm bài học theo từ khóa
     */
    PageResponse<Object> searchBaiHoc(String keyword, Pageable pageable);

    /**
     * Tìm kiếm bài học theo môn học và từ khóa
     */
    PageResponse<Object> searchBaiHocByMonHoc(Long idMonHoc, String keyword, Pageable pageable);

    /**
     * Lấy thông tin bài học theo ID
     */
    Object getBaiHocById(Long id);

    /**
     * Tạo bài học mới
     */
    Object createBaiHoc(BaiHocRequest request);

    /**
     * Cập nhật thông tin bài học
     */
    Object updateBaiHoc(Long id, BaiHocRequest request);

    /**
     * Xóa bài học
     */
    void deleteBaiHoc(Long id);

    /**
     * Kích hoạt/vô hiệu hóa bài học
     */
    Object toggleBaiHocStatus(Long id);

    // ==================== BUSINESS OPERATIONS ====================

    /**
     * Sao chép bài học
     */
    Object copyBaiHoc(Long id, BaiHocRequest request);

    /**
     * Sắp xếp lại thứ tự bài học trong môn
     */
    void reorderBaiHoc(Long idMonHoc, List<Long> orderedIds);

    /**
     * Di chuyển bài học lên/xuống
     */
    Object moveBaiHoc(Long id, String direction); // "up" hoặc "down"

    /**
     * Lấy bài học tiếp theo trong môn
     */
    Object getNextBaiHoc(Long idMonHoc, Integer currentStt);

    /**
     * Lấy bài học trước đó trong môn
     */
    Object getPreviousBaiHoc(Long idMonHoc, Integer currentStt);

    // ==================== STATISTICS ====================

    /**
     * Thống kê bài học theo môn học
     */
    Object getBaiHocStatistics(Long idMonHoc);

    /**
     * Thống kê tổng quan bài học
     */
    Object getBaiHocOverallStatistics();

    /**
     * Lấy tổng số tiết của tất cả bài học trong môn
     */
    Integer getTotalHoursByMonHoc(Long idMonHoc);

    // ==================== VALIDATION ====================

    /**
     * Kiểm tra mã bài học có tồn tại không
     */
    boolean existsByMaBaiHoc(String maBaiHoc);

    /**
     * Kiểm tra mã bài học có tồn tại không (loại trừ ID hiện tại)
     */
    boolean existsByMaBaiHocAndNotId(String maBaiHoc, Long excludeId);

    /**
     * Kiểm tra số thứ tự có trùng trong môn học không
     */
    boolean existsBySttInMonHoc(Long idMonHoc, Integer sttBaiHoc);

    /**
     * Kiểm tra số thứ tự có trùng trong môn học không (loại trừ ID hiện tại)
     */
    boolean existsBySttInMonHocAndNotId(Long idMonHoc, Integer sttBaiHoc, Long excludeId);

    // ==================== UTILITY ====================

    /**
     * Tự động tạo số thứ tự tiếp theo cho môn học
     */
    Integer generateNextSttForMonHoc(Long idMonHoc);

    /**
     * Tự động tạo mã bài học
     */
    String generateMaBaiHoc(Long idMonHoc, Integer sttBaiHoc);

    /**
     * Xuất danh sách bài học ra Excel
     */
    byte[] exportBaiHocToExcel(Long idMonHoc);

    /**
     * Import bài học từ Excel
     */
    Object importBaiHocFromExcel(Long idMonHoc, byte[] excelData);
}
