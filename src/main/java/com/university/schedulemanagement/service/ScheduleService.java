package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
/**
 * ScheduleService - Service xử lý lịch giảng
 */
public interface ScheduleService {

    /**
     * Tạo lịch giảng mới theo quy trình 7 bước
     */
    ScheduleResponse createSchedule(ScheduleRequest request);

    /**
     * Cập nhật lịch giảng
     */
    ScheduleResponse updateSchedule(Long id, ScheduleRequest request);

    /**
     * Xóa lịch giảng
     */
    void deleteSchedule(Long id);

    /**
     * Lấy lịch giảng theo giảng viên
     */
    List<ScheduleResponse> getSchedulesByTeacher(Long teacherId, Long semesterId);

    /**
     * L<PERSON>y lịch giảng theo lớp
     */
    List<ScheduleResponse> getSchedulesByClass(Long classId, Long semesterId);

    /**
     * L<PERSON>y lịch giảng theo học kỳ
     */
    Page<ScheduleResponse> getSchedulesBySemester(Long semesterId, Pageable pageable);

    /**
     * Kiểm tra xung đột lịch giảng
     */
    boolean checkScheduleConflict(ScheduleRequest request);

    /**
     * Lấy danh sách phòng học khả dụng
     */
    List<PhongHoc> getAvailableRooms(Long coSoId, String loaiPhong, Integer thuHoc, Long buoiId, Long hocKyId);

    /**
     * Export lịch giảng ra Excel
     */
    byte[] exportScheduleToExcel(Long teacherId, Long semesterId);

    /**
     * Lấy lịch giảng cá nhân (cho giảng viên đăng nhập)
     */
    List<ScheduleResponse> getPersonalSchedule(Long semesterId);

    /**
     * Lấy lịch giảng theo khoảng thời gian cho calendar view
     */
    List<Object> getScheduleCalendar(LocalDate startDate, LocalDate endDate, Long semesterId,
                                   Long teacherId, Long classId, Long departmentId);
}
