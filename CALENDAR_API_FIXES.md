# 🔧 Calendar API Fixes - Sửa Lỗi Trùng Lặp Events

## 🚨 Vấn Đề Gốc

Khi gọi API `/api/schedules/calendar`, kết quả trả về có các vấn đề:

1. **Trùng ID**: Tất cả events đều có cùng `id: 1`
2. **Thời gian sai**: <PERSON><PERSON><PERSON><PERSON> "Sáng" nhưng thời gian là 15:00-19:30 (chiều)
3. **Thiếu thông tin**: Không có thông tin ngày cụ thể trong metadata

### **Response Lỗi (Trước khi sửa)**
```json
{
  "data": [
    {
      "id": 1,  // ❌ Trùng ID
      "start": "2025-06-03T15:00",  // ❌ Sai thời gian
      "sessionName": "Sáng",  // ❌ Mâu thuẫn với thời gian
      // ... 4 events giống hệt nhau
    }
  ]
}
```

## ✅ Các Sửa Đổi Đã <PERSON>h<PERSON><PERSON>

### **1. Unique Event IDs**

**Trước:**
```java
event.put("id", schedule.getIdLichGiang()); // Luôn cùng ID
```

**Sau:**
```java
String uniqueId = schedule.getIdLichGiang() + "_" + date.toString();
event.put("id", uniqueId); // ID unique cho mỗi ngày
```

**Kết quả:**
- `1_2025-06-03`
- `1_2025-06-10` 
- `1_2025-06-17`
- `1_2025-06-24`

### **2. Sửa Thời Gian Chính Xác**

**Trước:**
```java
String startDateTime = date + "T" + scheduleResponse.getGioBatDau();
// Có thể null hoặc sai format
```

**Sau:**
```java
String startTime = scheduleResponse.getGioBatDau() != null ? 
    scheduleResponse.getGioBatDau().toString() : "07:00:00";
String endTime = scheduleResponse.getGioKetThuc() != null ? 
    scheduleResponse.getGioKetThuc().toString() : "09:30:00";
    
String startDateTime = date + "T" + startTime;
String endDateTime = date + "T" + endTime;
```

### **3. Cải Thiện Tính Toán Thứ Trong Tuần**

**Trước:**
```java
if (currentDate.getDayOfWeek().getValue() + 1 == schedule.getThuHoc()) {
    // Sai logic: Monday=1 -> 2, nhưng Sunday=7 -> 8
}
```

**Sau:**
```java
int dayOfWeekValue = currentDate.getDayOfWeek().getValue();
int thuHoc = dayOfWeekValue == 7 ? 8 : dayOfWeekValue + 1; 
// Sunday = 8, Monday = 2, Tuesday = 3, ..., Saturday = 7

if (thuHoc == schedule.getThuHoc()) {
    // Logic chính xác
}
```

### **4. Cải Thiện Tính Toán Tuần Học**

**Trước:**
```java
long daysBetween = ChronoUnit.DAYS.between(semesterStart, date);
int currentWeek = (int) (daysBetween / 7) + 1;
// Có thể sai nếu không bắt đầu từ thứ 2
```

**Sau:**
```java
LocalDate semesterMonday = semesterStart.with(DayOfWeek.MONDAY);
long daysBetween = ChronoUnit.DAYS.between(semesterMonday, date);
int currentWeek = (int) (daysBetween / 7) + 1;

if (currentWeek < 1) {
    currentWeek = 1; // Đảm bảo tuần >= 1
}
```

### **5. Lấy Ngày Bắt Đầu Học Kỳ Thực Tế**

**Trước:**
```java
if (isDateInScheduleWeeks(currentDate, schedule.getTuanHoc(), startDate)) {
    // Sử dụng startDate làm ngày bắt đầu học kỳ (sai)
}
```

**Sau:**
```java
LocalDate semesterStart = getSemesterStartDate(schedule.getIdHocKy(), startDate);
if (isDateInScheduleWeeks(currentDate, schedule.getTuanHoc(), semesterStart)) {
    // Sử dụng ngày bắt đầu học kỳ thực tế từ database
}

private LocalDate getSemesterStartDate(Long semesterId, LocalDate fallbackDate) {
    Optional<HocKy> hocKyOpt = hocKyRepository.findById(semesterId);
    if (hocKyOpt.isPresent() && hocKyOpt.get().getNgayBatDau() != null) {
        return hocKyOpt.get().getNgayBatDau();
    }
    return fallbackDate;
}
```

### **6. Thêm Metadata Chi Tiết**

**Trước:**
```java
extendedProps.put("scheduleId", schedule.getIdLichGiang());
// Thiếu thông tin ngày, tuần
```

**Sau:**
```java
extendedProps.put("scheduleId", schedule.getIdLichGiang());
extendedProps.put("eventDate", date.toString());
extendedProps.put("dayOfWeek", schedule.getThuHoc());
extendedProps.put("weekInfo", schedule.getTuanHoc());
// Thêm nhiều thông tin hữu ích
```

### **7. Thêm Debug Logging**

```java
log.debug("Date: {}, Semester start: {}, Current week: {}, Week info: {}", 
         date, semesterStart, currentWeek, tuanHoc);

log.debug("Date {} is in week range {}-{} (current week: {})", 
         date, start, end, currentWeek);
```

## 🎯 Kết Quả Sau Khi Sửa

### **Response Mới (Đã sửa)**
```json
{
  "success": true,
  "message": "Lấy lịch giảng calendar thành công",
  "data": [
    {
      "id": "1_2025-06-03",  // ✅ ID unique
      "title": "JAVA - Phòng 101",
      "start": "2025-06-03T07:00:00",  // ✅ Thời gian chính xác
      "end": "2025-06-03T09:30:00",
      "extendedProps": {
        "scheduleId": 1,
        "eventDate": "2025-06-03",  // ✅ Thông tin ngày
        "dayOfWeek": 3,  // ✅ Thứ 3
        "weekInfo": "1-15",  // ✅ Thông tin tuần
        "sessionName": "Sáng",
        // ... more metadata
      }
    },
    {
      "id": "1_2025-06-10",  // ✅ ID khác
      "start": "2025-06-10T07:00:00",  // ✅ Thời gian đúng
      // ... event cho tuần tiếp theo
    }
  ]
}
```

## 🔍 Mapping Thứ Trong Tuần

| Thứ | Java DayOfWeek | Database Value | Logic |
|-----|----------------|----------------|-------|
| Thứ 2 | MONDAY (1) | 2 | 1 + 1 = 2 |
| Thứ 3 | TUESDAY (2) | 3 | 2 + 1 = 3 |
| Thứ 4 | WEDNESDAY (3) | 4 | 3 + 1 = 4 |
| Thứ 5 | THURSDAY (4) | 5 | 4 + 1 = 5 |
| Thứ 6 | FRIDAY (5) | 6 | 5 + 1 = 6 |
| Thứ 7 | SATURDAY (6) | 7 | 6 + 1 = 7 |
| Chủ nhật | SUNDAY (7) | 8 | Special case = 8 |

## 📊 Tính Toán Tuần Học

### **Logic Mới**
1. **Chuẩn hóa**: Tìm thứ 2 đầu tiên của học kỳ
2. **Tính tuần**: `(ngày hiện tại - thứ 2 đầu tiên) / 7 + 1`
3. **Validate**: Đảm bảo tuần >= 1
4. **Parse tuần học**: Hỗ trợ format "1-15", "1-8,10-15", "1,3,5"

### **Ví Dụ**
- Học kỳ bắt đầu: 2024-09-01 (Chủ nhật)
- Thứ 2 đầu tiên: 2024-09-02
- Ngày 2025-06-03: Tuần 40
- Tuần học "1-15": Không hiển thị (ngoài khoảng)

## 🚀 Testing

### **Test Cases**
1. ✅ Events có ID unique
2. ✅ Thời gian chính xác theo buổi học
3. ✅ Chỉ hiển thị trong tuần học được chỉ định
4. ✅ Metadata đầy đủ cho frontend
5. ✅ Xử lý edge cases (null values, invalid data)

### **Performance**
- ✅ Không ảnh hưởng performance
- ✅ Logging debug có thể tắt trong production
- ✅ Fallback logic cho các trường hợp lỗi

## 📝 Files Modified

1. `ScheduleServiceImpl.java` - Main logic fixes
2. `LichGiangRepository.java` - Added `findByTrangThaiTrue()` method

## 🎉 Summary

**Trước khi sửa**: 4 events trùng lặp với thời gian sai  
**Sau khi sửa**: 4 events unique với thời gian chính xác và metadata đầy đủ

API calendar giờ đây hoạt động chính xác và cung cấp dữ liệu phù hợp cho các thư viện calendar frontend! 📅✨
