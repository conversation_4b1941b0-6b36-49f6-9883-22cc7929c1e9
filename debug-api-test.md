# 🔍 Debug API Test - Subjects Endpoint

## Vấn đề hiện tại
Frontend call API `/master-data/subjects?page=0&size=100` bị redirect đến `/error` endpoint.

## C<PERSON>c bước debug

### 1. <PERSON><PERSON><PERSON> tra Authentication
```bash
# Test login trước
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "maCanBo": "admin",
    "matKhau": "123456"
  }'
```

### 2. Test API với token
```bash
# Lấy token từ bước 1 và test
TOKEN="your_token_here"

curl -X GET "http://localhost:8080/api/master-data/subjects?page=0&size=100" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -v
```

### 3. Kiểm tra database có dữ liệu không
```sql
-- Kiểm tra bảng MON_HOC
SELECT COUNT(*) FROM MON_HOC;
SELECT * FROM MON_HOC LIMIT 5;

-- <PERSON><PERSON><PERSON> tra bảng KHOA (khoa)
SELECT COUNT(*) FROM KHOA;
SELECT * FROM KHOA LIMIT 5;
```

### 4. Test endpoint đơn giản hơn
```bash
# Test health check trước
curl -X GET http://localhost:8080/api/health

# Test departments (có thể đơn giản hơn)
curl -X GET "http://localhost:8080/api/master-data/departments?page=0&size=10" \
  -H "Authorization: Bearer $TOKEN"
```

## Nguyên nhân có thể

### 1. **Database rỗng**
- Chưa có dữ liệu trong bảng MON_HOC
- Chưa có dữ liệu trong bảng KHOA

### 2. **Mapping lỗi**
- Method `mapMonHocToResponse` có thể gây lỗi
- Lazy loading relationships gây exception

### 3. **Authentication/Authorization**
- Token không hợp lệ
- User không có quyền ADMIN hoặc TRUONG_KHOA

### 4. **JPA/Hibernate lỗi**
- Entity mapping không đúng
- Foreign key constraints

## Giải pháp tạm thời

### 1. Thêm dữ liệu test
```sql
-- Thêm khoa test
INSERT INTO KHOA (MA_KHOA, TEN_KHOA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) 
VALUES ('CNTT', 'Công nghệ thông tin', 1, NOW(), NOW());

-- Thêm môn học test
INSERT INTO MON_HOC (MA_MON_HOC, TEN_MON_HOC, ID_KHOA, ID_LOAI_MON_HOC, ID_HE_DAO_TAO, SO_TIET_LT, SO_TIET_TH, SO_TIET_TU, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT)
VALUES ('IT001', 'Lập trình Java', 1, 1, 1, 30, 15, 0, 1, NOW(), NOW());
```

### 2. Sửa method mapping
```java
private Object mapMonHocToResponse(MonHoc monHoc) {
    try {
        Map<String, Object> response = new HashMap<>();
        response.put("idMonHoc", monHoc.getIdMonHoc());
        response.put("maMonHoc", monHoc.getMaMonHoc());
        response.put("tenMonHoc", monHoc.getTenMonHoc());
        response.put("idKhoa", monHoc.getIdKhoa());
        response.put("soTietLt", monHoc.getSoTietLt() != null ? monHoc.getSoTietLt() : 0);
        response.put("soTietTh", monHoc.getSoTietTh() != null ? monHoc.getSoTietTh() : 0);
        response.put("soTietTu", monHoc.getSoTietTu() != null ? monHoc.getSoTietTu() : 0);
        response.put("trangThai", monHoc.getTrangThai());
        response.put("ngayTao", monHoc.getNgayTao());
        response.put("ngayCapNhat", monHoc.getNgayCapNhat());
        
        // Thêm thông tin khoa nếu có
        if (monHoc.getKhoa() != null) {
            response.put("tenKhoa", monHoc.getKhoa().getTenKhoa());
        }
        
        return response;
    } catch (Exception e) {
        log.error("Error mapping MonHoc to response: {}", e.getMessage());
        // Return basic info nếu có lỗi
        Map<String, Object> basicResponse = new HashMap<>();
        basicResponse.put("idMonHoc", monHoc.getIdMonHoc());
        basicResponse.put("maMonHoc", monHoc.getMaMonHoc());
        basicResponse.put("tenMonHoc", monHoc.getTenMonHoc());
        return basicResponse;
    }
}
```

### 3. Thêm logging chi tiết
```java
@Override
@Transactional(readOnly = true)
public PageResponse<Object> getAllSubjects(Long departmentId, Pageable pageable) {
    log.info("Getting all subjects for department: {}", departmentId);
    
    try {
        Page<MonHoc> monHocPage;
        
        if (departmentId != null) {
            log.debug("Fetching subjects for department: {}", departmentId);
            monHocPage = monHocRepository.findByIdKhoaWithPagination(departmentId, pageable);
        } else {
            log.debug("Fetching all subjects");
            monHocPage = monHocRepository.findAll(pageable);
        }
        
        log.debug("Found {} subjects", monHocPage.getTotalElements());
        
        List<Object> responses = monHocPage.getContent().stream()
                .map(this::mapMonHocToResponse)
                .collect(Collectors.toList());
        
        log.debug("Mapped {} subject responses", responses.size());
        
        return PageResponse.builder()
                .content(responses)
                .page(monHocPage.getNumber())
                .size(monHocPage.getSize())
                .totalElements(monHocPage.getTotalElements())
                .totalPages(monHocPage.getTotalPages())
                .first(monHocPage.isFirst())
                .last(monHocPage.isLast())
                .build();
                
    } catch (Exception e) {
        log.error("Error getting subjects: ", e);
        throw new RuntimeException("Lỗi khi lấy danh sách môn học: " + e.getMessage(), e);
    }
}
```

## Kiểm tra log

Xem log chi tiết để tìm nguyên nhân:
```bash
# Xem log realtime
tail -f logs/application.log

# Hoặc xem log trong console khi chạy ứng dụng
```

## Test script hoàn chỉnh

```bash
#!/bin/bash

echo "=== Testing Schedule Management API ==="

# 1. Health check
echo "1. Testing health check..."
curl -s http://localhost:8080/api/health | jq .

# 2. Login
echo "2. Testing login..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}')

echo $LOGIN_RESPONSE | jq .

# Extract token
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token')

if [ "$TOKEN" = "null" ]; then
    echo "❌ Login failed!"
    exit 1
fi

echo "✅ Login successful, token: ${TOKEN:0:20}..."

# 3. Test subjects API
echo "3. Testing subjects API..."
SUBJECTS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/master-data/subjects?page=0&size=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo $SUBJECTS_RESPONSE | jq .

# 4. Test departments API
echo "4. Testing departments API..."
DEPARTMENTS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/master-data/departments?page=0&size=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo $DEPARTMENTS_RESPONSE | jq .

echo "=== Test completed ==="
```

## Kết luận

Chạy các test trên để xác định chính xác nguyên nhân. Thường thì vấn đề sẽ là:
1. **Database rỗng** - cần thêm dữ liệu test
2. **Mapping error** - cần sửa method mapMonHocToResponse
3. **Authentication** - cần kiểm tra token và quyền user
