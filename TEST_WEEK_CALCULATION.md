# Test Tính Toán <PERSON>

## Vấn đề hiện tại
Cấu trúc lưu trữ thời khóa biểu không theo ngày cụ thể mà theo:
- `thuHoc`: <PERSON><PERSON><PERSON> trong tuần (2-8, tư<PERSON><PERSON>ứ 2 đến <PERSON>)
- `tuanHoc`: Chuỗi tuần học (VD: "1-15", "1-8,10-15", "1,3,5,7")

## C<PERSON>i thiện đã thực hiện

### 1. S<PERSON>a logic tính toán tuần học
```java
// Tìm thứ 2 đầu tiên của học kỳ
LocalDate semesterMonday = semesterStart.with(DayOfWeek.MONDAY);

// Nếu ngày bắt đầu không phải thứ 2, lùi về thứ 2 trước đó
if (semesterStart.getDayOfWeek() != DayOfWeek.MONDAY) {
    if (semesterMonday.isAfter(semesterStart)) {
        semesterMonday = semesterMonday.minusWeeks(1);
    }
}

long daysBetween = ChronoUnit.DAYS.between(semesterMonday, date);
int currentWeek = (int) (daysBetween / 7) + 1;
```

### 2. Thêm debug logging
```java
log.debug("Date: {}, Semester start: {}, Semester Monday: {}, Current week: {}, Week info: {}",
         date, semesterStart, semesterMonday, currentWeek, tuanHoc);
```

### 3. Thêm endpoint debug
```
GET /api/schedules/debug-week?date=2025-06-03&semesterId=1
```

## Test Cases

### Test 1: Ngày hiện tại
- **Input**: date=2025-06-03, semesterId=1
- **Expected**: Tuần 41 (như bạn đã nói)

### Test 2: Ngày trong tuần 23
- **Input**: date=2025-02-10 (giả sử), semesterId=1
- **Expected**: Tuần 23

### Test 3: Các trường hợp đặc biệt
- Học kỳ bắt đầu từ thứ 3, 4, 5...
- Ngày cuối tuần (Chủ nhật)
- Ngày đầu học kỳ

## Cách test

1. **Gọi API debug**:
```bash
curl -X GET "http://localhost:8080/api/schedules/debug-week?date=2025-06-03&semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

2. **Kiểm tra response**:
```json
{
  "success": true,
  "data": {
    "inputDate": "2025-06-03",
    "semesterName": "Học kỳ 2 năm 2024-2025",
    "semesterStart": "2024-08-26",
    "semesterMonday": "2024-08-26",
    "daysBetween": 281,
    "currentWeek": 41,
    "dayOfWeek": "TUESDAY",
    "thuHoc": 3
  }
}
```

3. **Kiểm tra calendar view**:
```bash
curl -X GET "http://localhost:8080/api/schedules/calendar?startDate=2025-06-01&endDate=2025-06-07&semesterId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Phương án 2: Thêm cột ngày cụ thể (Nếu cần)

Nếu logic tính toán vẫn không chính xác, có thể thêm cột lưu ngày cụ thể:

```sql
ALTER TABLE LICH_GIANG ADD COLUMN NGAY_HOC DATE;
```

```java
@Column(name = "NGAY_HOC")
private LocalDate ngayHoc; // Ngày học cụ thể
```

Nhưng cách này sẽ cần:
1. Migrate dữ liệu hiện có
2. Thay đổi logic tạo lịch
3. Cập nhật UI tạo lịch

## Kết luận

Đã cải thiện logic tính toán tuần học để chính xác hơn. Hãy test với endpoint debug để xác nhận kết quả.
