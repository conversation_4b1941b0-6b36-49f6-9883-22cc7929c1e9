-- 🗄️ Test Data for Schedule Management System
-- <PERSON>ữ liệu test cho hệ thống quản lý lịch giảng

-- ============================================================================
-- 1. KHOA/PHÒNG BAN (KHOA)
-- ============================================================================

INSERT INTO KHOA (MA_KHOA, TEN_KHOA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('CNTT', '<PERSON><PERSON><PERSON>ng ngh<PERSON> thông tin', 1, NOW(), NOW()),
('KTOAN', '<PERSON><PERSON><PERSON>án', 1, NOW(), NOW()),
('QTKD', '<PERSON><PERSON><PERSON> trị kinh doanh', 1, NOW(), NOW()),
('NGOAINGU', '<PERSON><PERSON><PERSON> ngữ', 1, NOW(), NOW()),
('LUAT', 'Khoa Luật', 1, NOW(), NOW());

-- ============================================================================
-- 2. VAI TRÒ (VAI_TRO)
-- ============================================================================

INSERT INTO VAI_TRO (TEN_VAI_TRO, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('ADMIN', 'Quản trị viên hệ thống', 1, NOW(), NOW()),
('TRUONG_KHOA', 'Trưởng khoa', 1, NOW(), NOW()),
('GIANG_VIEN', 'Giảng viên', 1, NOW(), NOW());

-- ============================================================================
-- 3. HỆ ĐÀO TẠO (HE_DAO_TAO)
-- ============================================================================

INSERT INTO HE_DAO_TAO (TEN_HE_DAO_TAO, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('Đại học chính quy', 'Hệ đại học chính quy 4 năm', 1, NOW(), NOW()),
('Đại học liên thông', 'Hệ đại học liên thông 2 năm', 1, NOW(), NOW()),
('Cao đẳng', 'Hệ cao đẳng 3 năm', 1, NOW(), NOW());

-- ============================================================================
-- 4. LOẠI MÔN HỌC (LOAI_MON_HOC)
-- ============================================================================

INSERT INTO LOAI_MON_HOC (TEN_LOAI_MON_HOC, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('Cơ sở ngành', 'Môn học cơ sở ngành', 1, NOW(), NOW()),
('Chuyên ngành', 'Môn học chuyên ngành', 1, NOW(), NOW()),
('Đại cương', 'Môn học đại cương', 1, NOW(), NOW()),
('Tự chọn', 'Môn học tự chọn', 1, NOW(), NOW());

-- ============================================================================
-- 5. HÌNH THỨC HỌC (HINH_THUC_HOC)
-- ============================================================================

INSERT INTO HINH_THUC_HOC (TEN_HINH_THUC, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('LT', 'Lý thuyết', 1, NOW(), NOW()),
('TH', 'Thực hành', 1, NOW(), NOW()),
('HT', 'Hỗn hợp (LT + TH)', 1, NOW(), NOW());

-- ============================================================================
-- 6. CƠ SỞ (CO_SO)
-- ============================================================================

INSERT INTO CO_SO (MA_CO_SO, TEN_CO_SO, DIA_CHI, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('CS1', 'Cơ sở 1 - Quận 1', '123 Nguyễn Huệ, Quận 1, TP.HCM', 1, NOW(), NOW()),
('CS2', 'Cơ sở 2 - Quận 7', '456 Nguyễn Thị Thập, Quận 7, TP.HCM', 1, NOW(), NOW()),
('CS3', 'Cơ sở 3 - Thủ Đức', '789 Võ Văn Ngân, Thủ Đức, TP.HCM', 1, NOW(), NOW());

-- ============================================================================
-- 7. PHÒNG HỌC (PHONG_HOC)
-- ============================================================================

INSERT INTO PHONG_HOC (MA_PHONG, TEN_PHONG, LOAI_PHONG, SUC_CHUA, ID_CO_SO, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
-- Cơ sở 1
('A101', 'Phòng A101', 'LT', 50, 1, 1, NOW(), NOW()),
('A102', 'Phòng A102', 'LT', 60, 1, 1, NOW(), NOW()),
('A201', 'Phòng A201', 'TH', 30, 1, 1, NOW(), NOW()),
('A202', 'Phòng A202', 'TH', 25, 1, 1, NOW(), NOW()),
-- Cơ sở 2
('B101', 'Phòng B101', 'LT', 80, 2, 1, NOW(), NOW()),
('B102', 'Phòng B102', 'LT', 70, 2, 1, NOW(), NOW()),
('B201', 'Phòng B201', 'TH', 40, 2, 1, NOW(), NOW()),
-- Cơ sở 3
('C101', 'Phòng C101', 'LT', 100, 3, 1, NOW(), NOW()),
('C201', 'Phòng C201', 'TH', 35, 3, 1, NOW(), NOW());

-- ============================================================================
-- 8. BUỔI HỌC (BUOI_HOC)
-- ============================================================================

INSERT INTO BUOI_HOC (TEN_BUOI, GIO_BAT_DAU, GIO_KET_THUC, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('Sáng', '07:00:00', '11:30:00', 1, NOW(), NOW()),
('Chiều', '13:00:00', '17:30:00', 1, NOW(), NOW()),
('Tối', '18:00:00', '21:30:00', 1, NOW(), NOW());

-- ============================================================================
-- 9. CÁN BỘ (CAN_BO) - USERS
-- ============================================================================

INSERT INTO CAN_BO (MA_CAN_BO, TEN, ID_VAI_TRO, ID_KHOA, MAT_KHAU, NU, SDT, EMAIL, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
-- Admin
('admin', 'Quản trị viên', 1, 1, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 0, '0901234567', '<EMAIL>', 1, NOW(), NOW()),

-- Trưởng khoa
('truongkhoa_cntt', 'Nguyễn Văn A', 2, 1, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 0, '0901234568', '<EMAIL>', 1, NOW(), NOW()),
('truongkhoa_ktoan', 'Trần Thị B', 2, 2, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 1, '0901234569', '<EMAIL>', 1, NOW(), NOW()),

-- Giảng viên CNTT
('gv001', 'Lê Văn C', 3, 1, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 0, '0901234570', '<EMAIL>', 1, NOW(), NOW()),
('gv002', 'Phạm Thị D', 3, 1, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 1, '0901234571', '<EMAIL>', 1, NOW(), NOW()),
('gv003', 'Hoàng Văn E', 3, 1, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 0, '0901234572', '<EMAIL>', 1, NOW(), NOW()),

-- Giảng viên Kế toán
('gv004', 'Nguyễn Thị F', 3, 2, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 1, '0901234573', '<EMAIL>', 1, NOW(), NOW()),
('gv005', 'Võ Văn G', 3, 2, '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6', 0, '0901234574', '<EMAIL>', 1, NOW(), NOW());

-- ============================================================================
-- 10. NIÊN KHÓA (NIEN_KHOA)
-- ============================================================================

INSERT INTO NIEN_KHOA (TEN_NIEN_KHOA, NAM, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('Niên khóa 2023-2024', 2023, 'Niên khóa học 2023-2024', 1, NOW(), NOW()),
('Niên khóa 2024-2025', 2024, 'Niên khóa học 2024-2025', 1, NOW(), NOW());

-- ============================================================================
-- 11. HỌC KỲ (HOC_KY)
-- ============================================================================

INSERT INTO HOC_KY (ID_NIEN_KHOA, TEN_HOC_KY, SO_TUAN, NGAY_BAT_DAU, NGAY_KET_THUC, HIEN_TAI, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
(1, 'Học kỳ 1 (2023-2024)', 15, '2023-09-01', '2023-12-31', 0, 'Học kỳ 1 năm học 2023-2024', 1, NOW(), NOW()),
(1, 'Học kỳ 2 (2023-2024)', 15, '2024-01-15', '2024-05-31', 0, 'Học kỳ 2 năm học 2023-2024', 1, NOW(), NOW()),
(2, 'Học kỳ 1 (2024-2025)', 15, '2024-09-01', '2024-12-31', 1, 'Học kỳ 1 năm học 2024-2025', 1, NOW(), NOW()),
(2, 'Học kỳ 2 (2024-2025)', 15, '2025-01-15', '2025-05-31', 0, 'Học kỳ 2 năm học 2024-2025', 1, NOW(), NOW());

-- ============================================================================
-- 12. MÔN HỌC (MON_HOC)
-- ============================================================================

INSERT INTO MON_HOC (MA_MON_HOC, TEN_MON_HOC, ID_KHOA, ID_LOAI_MON_HOC, ID_HE_DAO_TAO, SO_TIET_LT, SO_TIET_TH, SO_TIET_TU, MON_DIEU_KIEN, MON_TN, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
-- Môn học CNTT
('IT001', 'Lập trình Java cơ bản', 1, 1, 1, 30, 15, 0, 0, 0, 1, NOW(), NOW()),
('IT002', 'Cơ sở dữ liệu', 1, 1, 1, 30, 15, 0, 0, 0, 1, NOW(), NOW()),
('IT003', 'Lập trình Web', 1, 2, 1, 30, 30, 0, 1, 0, 1, NOW(), NOW()),
('IT004', 'Phân tích thiết kế hệ thống', 1, 2, 1, 45, 0, 0, 1, 0, 1, NOW(), NOW()),
('IT005', 'Lập trình Android', 1, 2, 1, 30, 30, 0, 1, 0, 1, NOW(), NOW()),

-- Môn học Kế toán
('KT001', 'Nguyên lý kế toán', 2, 1, 1, 45, 0, 0, 0, 0, 1, NOW(), NOW()),
('KT002', 'Kế toán tài chính', 2, 2, 1, 45, 15, 0, 1, 0, 1, NOW(), NOW()),
('KT003', 'Kế toán quản trị', 2, 2, 1, 45, 15, 0, 1, 0, 1, NOW(), NOW()),

-- Môn học đại cương
('DC001', 'Toán cao cấp A1', 1, 3, 1, 45, 0, 0, 0, 0, 1, NOW(), NOW()),
('DC002', 'Tiếng Anh 1', 4, 3, 1, 30, 15, 0, 0, 0, 1, NOW(), NOW()),
('DC003', 'Triết học Mác-Lênin', 1, 3, 1, 30, 0, 0, 0, 0, 1, NOW(), NOW());

-- ============================================================================
-- 13. NGÀNH HỌC (NGANH_HOC)
-- ============================================================================

INSERT INTO NGANH_HOC (MA_NGANH, TEN_NGANH, ID_KHOA, ID_HE_DAO_TAO, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
('CNTT', 'Công nghệ thông tin', 1, 1, 1, NOW(), NOW()),
('KTPM', 'Kỹ thuật phần mềm', 1, 1, 1, NOW(), NOW()),
('KTOAN', 'Kế toán', 2, 1, 1, NOW(), NOW()),
('QTKD', 'Quản trị kinh doanh', 3, 1, 1, NOW(), NOW());

-- ============================================================================
-- 14. LỚP HỌC (LOP_HOC)
-- ============================================================================

INSERT INTO LOP_HOC (MA_LOP, TEN_LOP, ID_KHOA, ID_NGANH_HOC, ID_HE_DAO_TAO, KHOA_HOC, SI_SO, ID_CAN_BO_CHUNHIEM, MO_TA, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
-- Lớp CNTT
('CNTT2023A', 'Lớp CNTT 2023A', 1, 1, 1, 2023, 45, 4, 'Lớp Công nghệ thông tin khóa 2023', 1, NOW(), NOW()),
('CNTT2023B', 'Lớp CNTT 2023B', 1, 1, 1, 2023, 42, 5, 'Lớp Công nghệ thông tin khóa 2023', 1, NOW(), NOW()),
('KTPM2023A', 'Lớp KTPM 2023A', 1, 2, 1, 2023, 38, 6, 'Lớp Kỹ thuật phần mềm khóa 2023', 1, NOW(), NOW()),

-- Lớp Kế toán
('KT2023A', 'Lớp KT 2023A', 2, 3, 1, 2023, 50, 7, 'Lớp Kế toán khóa 2023', 1, NOW(), NOW()),
('KT2023B', 'Lớp KT 2023B', 2, 3, 1, 2023, 48, 8, 'Lớp Kế toán khóa 2023', 1, NOW(), NOW());

-- ============================================================================
-- 15. LỊCH GIẢNG MẪU (LICH_GIANG)
-- ============================================================================

INSERT INTO LICH_GIANG (ID_CAN_BO, ID_MON_HOC, ID_LOP, ID_HOC_KY, ID_PHONG, ID_BUOI, ID_HINH_THUC, THU_HOC, SO_TIET, HE_SO, NHOM_TH, TUAN_HOC, GHI_CHU, TRANG_THAI, NGAY_TAO, NGAY_CAP_NHAT) VALUES
-- Lịch học kỳ hiện tại (ID_HOC_KY = 3)
(4, 1, 1, 3, 1, 1, 1, 2, 4, 1.0, NULL, '1-15', 'Lập trình Java cơ bản - Lý thuyết', 1, NOW(), NOW()),
(4, 1, 1, 3, 3, 2, 2, 4, 2, 1.0, 'TH1', '1-15', 'Lập trình Java cơ bản - Thực hành', 1, NOW(), NOW()),
(5, 2, 1, 3, 2, 1, 1, 3, 4, 1.0, NULL, '1-15', 'Cơ sở dữ liệu - Lý thuyết', 1, NOW(), NOW()),
(5, 2, 1, 3, 4, 2, 2, 5, 2, 1.0, 'TH1', '1-15', 'Cơ sở dữ liệu - Thực hành', 1, NOW(), NOW()),
(6, 3, 2, 3, 5, 1, 1, 2, 4, 1.0, NULL, '1-15', 'Lập trình Web - Lý thuyết', 1, NOW(), NOW()),
(7, 9, 4, 3, 6, 1, 1, 2, 4, 1.0, NULL, '1-15', 'Toán cao cấp A1', 1, NOW(), NOW()),
(8, 11, 5, 3, 7, 2, 1, 3, 4, 1.0, NULL, '1-15', 'Triết học Mác-Lênin', 1, NOW(), NOW());

-- ============================================================================
-- NOTES
-- ============================================================================

-- Mật khẩu mặc định cho tất cả user: "123456"
-- Hash: $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYoHPwk/y/zNw6

-- Test accounts:
-- admin / 123456 (ADMIN)
-- truongkhoa_cntt / 123456 (TRUONG_KHOA)
-- gv001 / 123456 (GIANG_VIEN)

-- Để test API:
-- 1. Login với admin/123456
-- 2. Call GET /api/master-data/subjects?page=0&size=10
-- 3. Sẽ trả về danh sách 11 môn học
