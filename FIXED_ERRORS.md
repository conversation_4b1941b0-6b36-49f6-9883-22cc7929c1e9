# 🔧 Đã Sửa Các Lỗi Backend

## ✅ Các Lỗi Đã Được Sửa

### 1. **Jakarta Persistence Migration**
**Vấn đề**: Spring Boot 3.x sử dụng `jakarta.persistence.*` thay vì `javax.persistence.*`

**Đã sửa trong các file**:
- ✅ `BaseEntity.java`
- ✅ `CanBo.java`
- ✅ `KHOA.java`
- ✅ `MonHoc.java`
- ✅ `LopHoc.java`
- ✅ `LichGiang.java`
- ✅ `PhongHoc.java`
- ✅ `VaiTro.java`
- ✅ `CoSo.java`
- ✅ `BuoiHoc.java`
- ✅ `HeDaoTao.java`
- ✅ `HinhThucHoc.java`
- ✅ `LoaiMonHoc.java`
- ✅ `Nhom.java`
- ✅ `NhomLK.java`
- ✅ `ThongKeGioGiang.java`
- ✅ `HocKy.java`
- ✅ `NienKhoa.java`
- ✅ `NganhHoc.java`

### 2. **Jakarta Validation Migration**
**Vấn đề**: Validation annotations cũng cần chuyển từ `javax.validation.*` sang `jakarta.validation.*`

**Đã sửa trong các file**:
- ✅ `AcademicYearRequest.java`
- ✅ `SemesterRequest.java`
- ✅ `DepartmentRequest.java`
- ✅ `ClassRequest.java`
- ✅ `RoomRequest.java`
- ✅ `CampusRequest.java`
- ✅ `TeacherRequest.java`

### 3. **Controller Validation Imports**
**Đã sửa trong các file**:
- ✅ `AcademicYearController.java`
- ✅ `SemesterController.java`
- ✅ `MasterDataController.java`

### 4. **Repository Methods**
**Đã thêm methods thiếu**:
- ✅ `PhongHocRepository.countByTrangThaiTrue()`
- ✅ `NienKhoaRepository` - các methods thống kê và validation
- ✅ `HocKyRepository` - methods quản lý học kỳ hiện tại
- ✅ `KhoaRepository` - methods validation và thống kê
- ✅ `LichGiangRepository` - methods thống kê cho dashboard
- ✅ `MonHocRepository` - methods quản lý theo khoa
- ✅ `LopHocRepository` - methods quản lý lớp học
- ✅ `CanBoRepository` - methods thống kê cán bộ

## 🚀 Hướng Dẫn Chạy Backend

### 1. **Kiểm tra Prerequisites**
```bash
# Kiểm tra Java version (cần Java 17+)
java -version

# Kiểm tra Maven
mvn -version

# Kiểm tra MySQL đang chạy
mysql --version
```

### 2. **Cấu hình Database**
Đảm bảo MySQL đang chạy và tạo database:
```sql
CREATE DATABASE schedule_management;
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'app_password';
GRANT ALL PRIVILEGES ON schedule_management.* TO 'app_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. **Cấu hình Application Properties**
Kiểm tra file `application.yml` có cấu hình đúng:
```yaml
spring:
  datasource:
    url: ***********************************************
    username: app_user
    password: app_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
```

### 4. **Chạy Backend**
```bash
# Từ thư mục backend
cd backend

# Clean và compile
mvn clean compile

# Chạy tests (optional)
mvn test

# Chạy application
mvn spring-boot:run
```

### 5. **Kiểm tra Backend đã chạy**
- **Health Check**: http://localhost:8080/api/health
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API Docs**: http://localhost:8080/v3/api-docs

### 6. **Test API Endpoints**
```bash
# Test health endpoint
curl http://localhost:8080/api/health

# Test login (nếu có user mặc định)
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}'

# Test admin dashboard (cần token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/admin/dashboard
```

## 🔍 Troubleshooting

### **Lỗi thường gặp và cách sửa**:

#### 1. **Port 8080 đã được sử dụng**
```bash
# Tìm process đang sử dụng port 8080
netstat -ano | findstr :8080

# Kill process (Windows)
taskkill /PID <PID> /F

# Hoặc đổi port trong application.yml
server:
  port: 8081
```

#### 2. **Database connection failed**
- Kiểm tra MySQL đang chạy
- Kiểm tra username/password trong application.yml
- Kiểm tra database đã được tạo

#### 3. **Bean creation failed**
- Kiểm tra tất cả imports đã đúng (jakarta.* thay vì javax.*)
- Kiểm tra các @Repository, @Service, @Controller annotations
- Kiểm tra circular dependencies

#### 4. **Validation errors**
- Kiểm tra tất cả validation imports đã chuyển sang jakarta.validation.*
- Kiểm tra @Valid annotations trong controllers

#### 5. **JPA/Hibernate errors**
- Kiểm tra entity mappings
- Kiểm tra foreign key constraints
- Kiểm tra table names và column names

## 📋 Checklist Trước Khi Chạy

- [ ] Java 17+ đã cài đặt
- [ ] Maven đã cài đặt
- [ ] MySQL đang chạy
- [ ] Database `schedule_management` đã được tạo
- [ ] User `app_user` đã được tạo với quyền phù hợp
- [ ] Tất cả imports đã chuyển sang jakarta.*
- [ ] Application.yml có cấu hình đúng
- [ ] Port 8080 không bị conflict

## 🎯 Kết Quả Mong Đợi

Sau khi sửa tất cả lỗi trên, backend sẽ:
- ✅ Start thành công trên port 8080
- ✅ Kết nối database thành công
- ✅ Tạo tables tự động (nếu ddl-auto: update)
- ✅ Swagger UI accessible tại /swagger-ui.html
- ✅ Tất cả API endpoints hoạt động
- ✅ Authentication và authorization hoạt động

## 📞 Hỗ Trợ

Nếu vẫn gặp lỗi, hãy kiểm tra:
1. **Console logs** để xem lỗi cụ thể
2. **Application.yml** cấu hình
3. **Database connection**
4. **Java và Maven versions**
5. **Port conflicts**

Các lỗi phổ biến đã được sửa, backend sẽ chạy thành công! 🚀
