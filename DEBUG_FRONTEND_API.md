# 🔍 Debug Frontend API Call Issue

## 🚨 Vấn đề hiện tại

Frontend call API `/master-data/subjects?page=0&size=100` bị redirect đến `/error` endpoint.

**Log hiện tại:**
```
Authorized filter invocation [GET /master-data/subjects?page=0&size=100] with attributes [authenticated]
Secured GET /master-data/subjects?page=0&size=100
Cleared SecurityContextHolder to complete request
Securing GET /error?page=0&size=100
```

## 🔧 Các bước debug đã thực hiện

### 1. ✅ Đã sửa MasterDataServiceImpl
- Thêm null checks cho tất cả fields
- Thêm try-catch để handle lazy loading exceptions
- Thêm logging chi tiết

### 2. ✅ Đã tạo test data
- File `test-data.sql` với dữ liệu mẫu đầy đủ
- 11 môn học, 5 khoa, 8 giảng viên, 4 học kỳ
- Accounts test: admin/123456, gv001/123456

### 3. ✅ Đã tạo test script
- File `test-api.sh` để test tự động tất cả endpoints
- <PERSON><PERSON><PERSON> tra authentication, authorization
- Test từng API một cách có hệ thống

## 🚀 Cách khắc phục

### Bước 1: Chạy test data
```bash
# Kết nối MySQL và chạy script
mysql -u root -p lich_giang_db < test-data.sql
```

### Bước 2: Restart ứng dụng
```bash
# Stop application (Ctrl+C)
# Start lại
mvn spring-boot:run
```

### Bước 3: Chạy test script
```bash
# Chạy test tự động
./test-api.sh

# Hoặc test manual
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}'

# Lấy token và test subjects API
curl -X GET "http://localhost:8080/api/master-data/subjects?page=0&size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔍 Nguyên nhân có thể

### 1. **Database rỗng** ✅ FIXED
- **Vấn đề**: Không có dữ liệu trong bảng MON_HOC
- **Giải pháp**: Chạy test-data.sql

### 2. **Mapping error** ✅ FIXED
- **Vấn đề**: Method mapMonHocToResponse gây exception
- **Giải pháp**: Thêm null checks và try-catch

### 3. **Lazy loading exception** ✅ FIXED
- **Vấn đề**: Truy cập relationship khi session đã đóng
- **Giải pháp**: Thêm fallback query trong mapping

### 4. **Authentication issue**
- **Vấn đề**: Token không hợp lệ hoặc expired
- **Giải pháp**: Kiểm tra login và token

## 📊 Expected Results

Sau khi fix, API `/master-data/subjects` sẽ trả về:

```json
{
  "success": true,
  "message": "Lấy danh sách môn học thành công",
  "data": {
    "content": [
      {
        "idMonHoc": 1,
        "maMonHoc": "IT001",
        "tenMonHoc": "Lập trình Java cơ bản",
        "idKhoa": 1,
        "soTietLt": 30,
        "soTietTh": 15,
        "soTietTu": 0,
        "trangThai": true,
        "tenKhoa": "Khoa Công nghệ thông tin",
        "maKhoa": "CNTT"
      }
    ],
    "page": 0,
    "size": 100,
    "totalElements": 11,
    "totalPages": 1,
    "first": true,
    "last": true,
    "hasNext": false,
    "hasPrevious": false
  }
}
```

## 🔧 Debug Commands

### Kiểm tra database
```sql
-- Kiểm tra có dữ liệu không
SELECT COUNT(*) FROM MON_HOC;
SELECT COUNT(*) FROM KHOA;
SELECT COUNT(*) FROM CAN_BO;

-- Xem dữ liệu mẫu
SELECT * FROM MON_HOC LIMIT 5;
SELECT * FROM KHOA LIMIT 5;
```

### Kiểm tra logs
```bash
# Xem logs realtime
tail -f logs/application.log

# Hoặc xem trong console
# Tìm dòng có "Error getting subjects"
```

### Test manual
```bash
# 1. Health check
curl http://localhost:8080/api/health

# 2. Login
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}'

# 3. Test subjects với token
curl -X GET "http://localhost:8080/api/master-data/subjects?page=0&size=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -v
```

## 📝 Files đã tạo

1. **debug-api-test.md** - Hướng dẫn debug chi tiết
2. **test-data.sql** - Dữ liệu test đầy đủ
3. **test-api.sh** - Script test tự động
4. **DEBUG_FRONTEND_API.md** - File này

## ✅ Checklist

- [x] Sửa MasterDataServiceImpl mapping
- [x] Thêm null checks và error handling
- [x] Tạo test data đầy đủ
- [x] Tạo test script tự động
- [ ] Chạy test-data.sql
- [ ] Restart application
- [ ] Chạy test script
- [ ] Verify API hoạt động

## 🎯 Kết quả mong đợi

Sau khi thực hiện các bước trên:

1. ✅ API `/master-data/subjects` trả về data thành công
2. ✅ Frontend có thể load danh sách môn học
3. ✅ Không còn redirect đến `/error`
4. ✅ Tất cả master data APIs hoạt động bình thường

## 📞 Nếu vẫn lỗi

1. **Kiểm tra database connection**
   ```bash
   mysql -u root -p -e "USE lich_giang_db; SHOW TABLES;"
   ```

2. **Kiểm tra application startup logs**
   - Tìm errors khi start application
   - Kiểm tra JPA entity mapping

3. **Test với Postman/Insomnia**
   - Import API collection
   - Test từng endpoint riêng lẻ

4. **Enable debug logging**
   ```properties
   logging.level.com.university.schedulemanagement=DEBUG
   logging.level.org.hibernate.SQL=DEBUG
   ```

## 🚀 Tóm tắt

**Root cause**: Database rỗng + mapping method không handle null values

**Solution**: 
1. Insert test data
2. Fix mapping với null checks
3. Add proper error handling

**Verification**: Chạy test-api.sh để verify tất cả APIs hoạt động
