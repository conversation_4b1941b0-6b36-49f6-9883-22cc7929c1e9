-- =====================================================
-- Script tạo bảng BAI_HOC và các ràng buộc liên quan
-- =====================================================

-- Tạo bảng BAI_HOC
CREATE TABLE BAI_HOC (
    ID_BAI_HOC BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID bài học',
    ID_MON_HOC BIGINT NOT NULL COMMENT 'ID môn học',
    MA_BAI_HOC VARCHAR(20) NOT NULL COMMENT 'Mã bài học',
    TEN_BAI_HOC VARCHAR(250) NOT NULL COMMENT 'Tên bài học',
    STT_BAI_HOC INT COMMENT '<PERSON><PERSON> thứ tự bài học trong môn',
    SO_TIET_LT INT DEFAULT 0 COMMENT 'Số tiết lý thuyết',
    SO_TIET_TH INT DEFAULT 0 COMMENT 'Số tiết thực hành',
    SO_TIET_TU INT DEFAULT 0 COMMENT 'Số tiết tự học',
    MUC_TIEU TEXT COMMENT 'Mục tiêu bài học',
    NOI_DUNG TEXT COMMENT 'Nội dung chi tiết',
    PHUONG_PHAP_DAY TEXT COMMENT 'Phương pháp giảng dạy',
    TAI_LIEU_THAM_KHAO TEXT COMMENT 'Tài liệu tham khảo',
    BAI_TAP TEXT COMMENT 'Bài tập về nhà',
    DANH_GIA TEXT COMMENT 'Phương pháp đánh giá',
    GHI_CHU TEXT COMMENT 'Ghi chú thêm',
    TRANG_THAI BOOLEAN DEFAULT TRUE COMMENT 'Trạng thái hoạt động',
    NGAY_TAO TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày tạo',
    NGAY_CAP_NHAT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
    
    -- Indexes
    INDEX idx_bai_hoc_mon_hoc (ID_MON_HOC),
    INDEX idx_bai_hoc_ma (MA_BAI_HOC),
    INDEX idx_bai_hoc_stt (STT_BAI_HOC),
    INDEX idx_bai_hoc_trang_thai (TRANG_THAI),
    INDEX idx_bai_hoc_ngay_tao (NGAY_TAO),
    
    -- Unique constraints
    UNIQUE KEY uk_bai_hoc_ma (MA_BAI_HOC),
    UNIQUE KEY uk_bai_hoc_mon_stt (ID_MON_HOC, STT_BAI_HOC),
    
    -- Foreign key constraints
    CONSTRAINT fk_bai_hoc_mon_hoc 
        FOREIGN KEY (ID_MON_HOC) 
        REFERENCES MON_HOC(ID_MON_HOC) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_bai_hoc_so_tiet_lt CHECK (SO_TIET_LT >= 0),
    CONSTRAINT chk_bai_hoc_so_tiet_th CHECK (SO_TIET_TH >= 0),
    CONSTRAINT chk_bai_hoc_so_tiet_tu CHECK (SO_TIET_TU >= 0),
    CONSTRAINT chk_bai_hoc_stt CHECK (STT_BAI_HOC > 0),
    CONSTRAINT chk_bai_hoc_co_tiet CHECK (
        SO_TIET_LT > 0 OR SO_TIET_TH > 0 OR SO_TIET_TU > 0
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Bảng quản lý bài học';

-- =====================================================
-- Tạo các trigger để tự động cập nhật thời gian
-- =====================================================

DELIMITER $$

-- Trigger cập nhật NGAY_CAP_NHAT khi update
CREATE TRIGGER tr_bai_hoc_update_time 
    BEFORE UPDATE ON BAI_HOC
    FOR EACH ROW
BEGIN
    SET NEW.NGAY_CAP_NHAT = CURRENT_TIMESTAMP;
END$$

DELIMITER ;

-- =====================================================
-- Tạo stored procedures hỗ trợ
-- =====================================================

DELIMITER $$

-- Procedure lấy số thứ tự tiếp theo cho môn học
CREATE PROCEDURE sp_get_next_stt_bai_hoc(
    IN p_id_mon_hoc BIGINT,
    OUT p_next_stt INT
)
BEGIN
    DECLARE v_max_stt INT DEFAULT 0;
    
    SELECT COALESCE(MAX(STT_BAI_HOC), 0) INTO v_max_stt
    FROM BAI_HOC 
    WHERE ID_MON_HOC = p_id_mon_hoc;
    
    SET p_next_stt = v_max_stt + 1;
END$$

-- Procedure thống kê bài học theo môn
CREATE PROCEDURE sp_thong_ke_bai_hoc_mon(
    IN p_id_mon_hoc BIGINT
)
BEGIN
    SELECT 
        COUNT(*) as tong_bai_hoc,
        COUNT(CASE WHEN TRANG_THAI = TRUE THEN 1 END) as bai_hoc_hoat_dong,
        COUNT(CASE WHEN TRANG_THAI = FALSE THEN 1 END) as bai_hoc_khong_hoat_dong,
        SUM(SO_TIET_LT) as tong_tiet_lt,
        SUM(SO_TIET_TH) as tong_tiet_th,
        SUM(SO_TIET_TU) as tong_tiet_tu,
        SUM(SO_TIET_LT + SO_TIET_TH + SO_TIET_TU) as tong_tat_ca_tiet,
        AVG(SO_TIET_LT + SO_TIET_TH + SO_TIET_TU) as trung_binh_tiet_bai_hoc,
        MIN(STT_BAI_HOC) as stt_nho_nhat,
        MAX(STT_BAI_HOC) as stt_lon_nhat
    FROM BAI_HOC 
    WHERE ID_MON_HOC = p_id_mon_hoc;
END$$

-- Procedure sắp xếp lại số thứ tự bài học
CREATE PROCEDURE sp_reorder_bai_hoc(
    IN p_id_mon_hoc BIGINT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id_bai_hoc BIGINT;
    DECLARE v_new_stt INT DEFAULT 1;
    
    DECLARE cur_bai_hoc CURSOR FOR 
        SELECT ID_BAI_HOC 
        FROM BAI_HOC 
        WHERE ID_MON_HOC = p_id_mon_hoc 
        ORDER BY STT_BAI_HOC ASC, NGAY_TAO ASC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Tạm thời disable unique constraint check
    SET @old_unique_checks = @@unique_checks;
    SET unique_checks = 0;
    
    OPEN cur_bai_hoc;
    
    read_loop: LOOP
        FETCH cur_bai_hoc INTO v_id_bai_hoc;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE BAI_HOC 
        SET STT_BAI_HOC = v_new_stt 
        WHERE ID_BAI_HOC = v_id_bai_hoc;
        
        SET v_new_stt = v_new_stt + 1;
    END LOOP;
    
    CLOSE cur_bai_hoc;
    
    -- Restore unique constraint check
    SET unique_checks = @old_unique_checks;
END$$

DELIMITER ;

-- =====================================================
-- Tạo view hỗ trợ
-- =====================================================

-- View thống kê bài học theo môn học
CREATE VIEW v_bai_hoc_thong_ke AS
SELECT 
    mh.ID_MON_HOC,
    mh.MA_MON_HOC,
    mh.TEN_MON_HOC,
    COUNT(bh.ID_BAI_HOC) as SO_BAI_HOC,
    COUNT(CASE WHEN bh.TRANG_THAI = TRUE THEN 1 END) as SO_BAI_HOC_HOAT_DONG,
    COALESCE(SUM(bh.SO_TIET_LT), 0) as TONG_TIET_LT,
    COALESCE(SUM(bh.SO_TIET_TH), 0) as TONG_TIET_TH,
    COALESCE(SUM(bh.SO_TIET_TU), 0) as TONG_TIET_TU,
    COALESCE(SUM(bh.SO_TIET_LT + bh.SO_TIET_TH + bh.SO_TIET_TU), 0) as TONG_TAT_CA_TIET,
    CASE 
        WHEN COUNT(bh.ID_BAI_HOC) > 0 
        THEN ROUND(SUM(bh.SO_TIET_LT + bh.SO_TIET_TH + bh.SO_TIET_TU) / COUNT(bh.ID_BAI_HOC), 2)
        ELSE 0 
    END as TRUNG_BINH_TIET_BAI_HOC
FROM MON_HOC mh
LEFT JOIN BAI_HOC bh ON mh.ID_MON_HOC = bh.ID_MON_HOC
GROUP BY mh.ID_MON_HOC, mh.MA_MON_HOC, mh.TEN_MON_HOC;

-- View chi tiết bài học với thông tin môn học
CREATE VIEW v_bai_hoc_chi_tiet AS
SELECT 
    bh.*,
    mh.MA_MON_HOC,
    mh.TEN_MON_HOC,
    (bh.SO_TIET_LT + bh.SO_TIET_TH + bh.SO_TIET_TU) as TONG_SO_TIET,
    CASE 
        WHEN bh.SO_TIET_TH > 0 THEN TRUE 
        ELSE FALSE 
    END as CO_THUC_HANH,
    CASE 
        WHEN bh.SO_TIET_LT > 0 THEN TRUE 
        ELSE FALSE 
    END as CO_LY_THUYET,
    CASE 
        WHEN bh.SO_TIET_TU > 0 THEN TRUE 
        ELSE FALSE 
    END as CO_TU_HOC
FROM BAI_HOC bh
INNER JOIN MON_HOC mh ON bh.ID_MON_HOC = mh.ID_MON_HOC;

-- =====================================================
-- Insert dữ liệu mẫu (tùy chọn)
-- =====================================================

-- Uncomment để thêm dữ liệu mẫu
/*
-- Giả sử có môn học với ID = 1 (Lập trình Java)
INSERT INTO BAI_HOC (ID_MON_HOC, MA_BAI_HOC, TEN_BAI_HOC, STT_BAI_HOC, SO_TIET_LT, SO_TIET_TH, SO_TIET_TU, MUC_TIEU, NOI_DUNG) VALUES
(1, 'BH001', 'Giới thiệu về Java', 1, 2, 1, 1, 'Hiểu được tổng quan về ngôn ngữ Java', 'Lịch sử, đặc điểm, ưu nhược điểm của Java'),
(1, 'BH002', 'Cài đặt môi trường phát triển', 2, 1, 2, 1, 'Cài đặt và cấu hình JDK, IDE', 'Hướng dẫn cài đặt JDK, Eclipse/IntelliJ'),
(1, 'BH003', 'Cú pháp cơ bản Java', 3, 3, 2, 2, 'Nắm vững cú pháp cơ bản của Java', 'Biến, kiểu dữ liệu, toán tử, cấu trúc điều khiển');
*/

-- =====================================================
-- Kết thúc script
-- =====================================================

-- Hiển thị thông tin bảng đã tạo
SHOW CREATE TABLE BAI_HOC;

-- Hiển thị các index
SHOW INDEX FROM BAI_HOC;

-- Hiển thị các view
SHOW CREATE VIEW v_bai_hoc_thong_ke;
SHOW CREATE VIEW v_bai_hoc_chi_tiet;
